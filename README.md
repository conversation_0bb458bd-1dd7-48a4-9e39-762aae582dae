# 记分小程序

一个用于记录麻将、扑克等游戏分数的微信小程序。

## 功能特性

### 游戏创建
- 支持多种游戏类型：麻将、扑克、棋类、其他
- 可设置游戏名称、玩家人数、初始分数
- **新增：玩家选择方式**
  - 本地玩家：在同一设备上设置玩家
  - 邀请好友：邀请好友加入游戏

### 邀请好友功能
- 生成邀请链接分享给好友
- 生成小程序码供好友扫描
- 微信分享功能
- 实时查看已邀请玩家状态
- 人数满足后可开始游戏

### 记分功能
- 快速记分：支持赢/输模式和自定义分数
- 多玩家选择：可同时为多个玩家记分
- 分数平衡检查：确保每局分数总和为0
- 历史记录：查看每局详细记分情况

### 游戏模式
- **本地模式**：传统的单设备多玩家记分
- **邀请模式**：每位玩家可在自己的设备上操作记分

## 使用流程

### 本地模式
1. 创建新游戏 → 选择"本地玩家"
2. 设置玩家信息
3. 开始记分

### 邀请模式
1. 创建新游戏 → 选择"邀请好友"
2. 生成邀请链接或小程序码
3. 分享给好友
4. 好友点击链接加入游戏
5. 人数满足后开始游戏
6. 每位玩家可在自己设备上记分

## 技术实现

- 前端：微信小程序原生开发
- 数据存储：本地存储（localStorage）
- 邀请功能：基于唯一邀请ID的本地存储实现
- 分享功能：微信小程序分享API

## 注意事项

- 邀请功能目前基于本地存储实现，实际项目中建议使用云开发或后端服务
- 小程序码生成需要调用微信官方接口
- 邀请链接需要配置实际的域名和路径 