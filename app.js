// app.js
App({
    globalData: {
        userInfo: null,
        apiBaseUrl: 'http://localhost:4220/api/v1'
        // apiBaseUrl: 'https://scoresnap.pomfret.cn/api/v1'
    },

    onLaunch() {
        // 检查网络状态
        wx.getNetworkType({
            success: (res) => {
                const networkType = res.networkType;
                if (networkType === 'none') {
                    wx.showToast({
                        title: '当前无网络连接',
                        icon: 'none',
                        duration: 2000
                    });
                }
            }
        });

        // 监听网络状态变化
        wx.onNetworkStatusChange((res) => {
            if (!res.isConnected) {
                wx.showToast({
                    title: '网络连接已断开',
                    icon: 'none',
                    duration: 2000
                });
            } else {
                console.log('网络已连接，类型为：', res.networkType);
            }
        });
    }
})