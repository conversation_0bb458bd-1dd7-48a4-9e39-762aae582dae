/**app.wxss**/
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  background-color: #f8f8f8;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
}

/* 移除所有元素的点击高亮效果 */
view, text, button, input, textarea, image, navigator, label, form, checkbox, radio, slider, picker {
  -webkit-tap-highlight-color: transparent;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 0 20rpx 0;
  box-sizing: border-box;
}

.safe-area {
  padding-top: 44px; /* 状态栏高度 */
}

.status-bar {
  height: 44px;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  background-color: #ffffff;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  position: fixed;
  top: 44px;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.content {
  flex: 1;
  padding: 0 32rpx;
  margin-top: 88px; /* 状态栏+导航栏 */
}

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-primary {
  background-color: #1296db;
  color: #ffffff;
}

.btn-outline {
  background-color: transparent;
  color: #1296db;
  border: 1px solid #1296db;
}

.btn-block {
  width: 100%;
  margin: 24rpx 0;
}

.card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.mt-2 {
  margin-top: 16rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.py-2 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}

.px-4 {
  padding-left: 32rpx;
  padding-right: 32rpx;
} 
