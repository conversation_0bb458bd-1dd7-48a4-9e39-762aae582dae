# 自定义胶囊导航栏组件

一个高度可复用的微信小程序胶囊状导航栏组件，支持返回和回到首页功能，完美适配微信原生胶囊按钮位置。

## 功能特性

- ✅ **胶囊状设计**：模仿微信原生胶囊按钮样式
- ✅ **自适应布局**：自动适配不同设备的状态栏和胶囊按钮位置
- ✅ **返回功能**：支持返回上一页，智能处理页面栈
- ✅ **首页功能**：支持回到首页，自动识别 tabBar 页面
- ✅ **高度可复用**：通过属性配置满足不同页面需求
- ✅ **事件支持**：支持自定义返回和首页事件
- ✅ **插槽支持**：支持右侧自定义内容
- ✅ **深色模式**：自动适配深色模式
- ✅ **响应式设计**：适配不同屏幕尺寸

## 使用方法

### 1. 基础用法

```xml
<!-- 最简单的用法 -->
<custom-navbar title="页面标题"></custom-navbar>
```

### 2. 完整配置

```xml
<custom-navbar 
  title="创建新游戏"
  background="#ffffff"
  color="#333333"
  show-back="{{true}}"
  show-home="{{true}}"
  show-capsule="{{true}}"
  show-right-slot="{{false}}"
  delta="{{1}}"
  home-path="/pages/gameList/index"
  custom-back="{{false}}"
  bind:back="onNavBack"
  bind:home="onNavHome"
>
  <!-- 右侧插槽内容 -->
  <view slot="right">
    <text>自定义内容</text>
  </view>
</custom-navbar>
```

### 3. 页面适配

在使用组件的页面中，需要调整内容区域的高度：

```xml
<!-- 内容区域需要减去导航栏高度 -->
<scroll-view style="height: calc(100vh - {{navbarHeight}}px);">
  <!-- 页面内容 -->
</scroll-view>
```

在页面 JS 中获取导航栏高度：

```javascript
// 在页面的 lifetimes.attached 或 onLoad 中调用
getNavbarHeight() {
  setTimeout(() => {
    const navbar = this.selectComponent('custom-navbar');
    if (navbar) {
      const height = navbar.getNavbarHeight();
      this.setData({ navbarHeight: height });
    }
  }, 100);
}
```

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 页面标题 |
| background | String | '#ffffff' | 背景颜色 |
| color | String | '#333333' | 文字颜色 |
| showBack | Boolean | true | 是否显示返回按钮 |
| showHome | Boolean | true | 是否显示首页按钮 |
| showCapsule | Boolean | true | 是否显示胶囊按钮组 |
| showRightSlot | Boolean | false | 是否显示右侧插槽 |
| delta | Number | 1 | 返回页面层级 |
| homePath | String | '/pages/gameList/index' | 首页路径 |
| customBack | Boolean | false | 是否使用自定义返回事件 |

## 事件说明

| 事件名 | 说明 | 参数 |
|--------|------|------|
| back | 返回按钮点击事件 | { delta: Number } |
| home | 首页按钮点击事件 | { path: String } |

## 使用场景

### 1. 只显示返回按钮

```xml
<custom-navbar 
  title="页面标题"
  show-back="{{true}}"
  show-home="{{false}}"
></custom-navbar>
```

### 2. 只显示首页按钮

```xml
<custom-navbar 
  title="页面标题"
  show-back="{{false}}"
  show-home="{{true}}"
></custom-navbar>
```

### 3. 自定义返回逻辑

```xml
<custom-navbar 
  title="页面标题"
  custom-back="{{true}}"
  bind:back="handleCustomBack"
></custom-navbar>
```

```javascript
handleCustomBack(e) {
  // 自定义返回逻辑
  console.log('自定义返回', e.detail);
  // 例如：显示确认对话框
  wx.showModal({
    title: '确认返回',
    content: '确定要离开当前页面吗？',
    success: (res) => {
      if (res.confirm) {
        wx.navigateBack();
      }
    }
  });
}
```

### 4. 带右侧自定义内容

```xml
<custom-navbar 
  title="页面标题"
  show-right-slot="{{true}}"
>
  <view slot="right" bindtap="handleRightAction">
    <text>保存</text>
  </view>
</custom-navbar>
```

## 注意事项

1. **页面配置**：使用组件的页面需要设置 `"navigationStyle": "custom"`
2. **高度适配**：页面内容需要减去导航栏高度，避免被遮挡
3. **事件处理**：组件会自动处理大部分场景，特殊需求可通过事件自定义
4. **性能优化**：组件会缓存计算结果，避免重复计算

## 兼容性

- 支持微信小程序基础库 2.0.0 及以上版本
- 自动适配 iPhone X 系列的安全区域
- 支持深色模式自动切换
