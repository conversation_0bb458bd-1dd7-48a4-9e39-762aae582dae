// components/custom-navbar/custom-navbar.js
const app = getApp();
const router = require('../../utils/router');

Component({
    options: {
        multipleSlots: true, // 启用多slot支持
        styleIsolation: 'apply-shared'
    },

    /**
     * 组件的属性列表
     */
    properties: {
        // 页面标题
        title: {
            type: String,
            value: ''
        },
        // 背景颜色
        background: {
            type: String,
            value: '#ffffff'
        },
        // 文字颜色
        color: {
            type: String,
            value: '#333333'
        },
        // 是否显示返回按钮
        showBack: {
            type: Boolean,
            value: true
        },
        // 是否显示首页按钮
        showHome: {
            type: Boolean,
            value: true
        },
        // 是否显示胶囊按钮组
        showCapsule: {
            type: Boolean,
            value: true
        },
        // 是否显示右侧插槽
        showRightSlot: {
            type: Boolean,
            value: false
        },
        // 返回页面层级
        delta: {
            type: Number,
            value: 1
        },
        // 首页路径
        homePath: {
            type: String,
            value: '/pages/gameList/index'
        },
        // 自定义返回事件
        customBack: {
            type: Boolean,
            value: false
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        statusBarHeight: 0, // 状态栏高度
        navbarHeight: 0, // 整个导航栏高度
        customNavHeight: 0, // 自定义导航内容高度
        titleMaxWidth: 0, // 标题最大宽度
        capsuleStyle: { // 胶囊样式
            height: 32,
            top: 6,
            left: 24,
            borderRadius: 16,
            btnHeight: 28,
            btnWidth: 28,
            dividerHeight: 20
        }
    },

    /**
     * 组件生命周期
     */
    lifetimes: {
        attached() {
            this.initNavbar();
        }
    },

    /**
     * 组件的方法列表
     */
    methods: {
        /**
         * 初始化导航栏
         */
        initNavbar() {
            try {
                // 获取系统信息
                const systemInfo = wx.getSystemInfoSync();
                const statusBarHeight = systemInfo.statusBarHeight || 20;

                // 获取胶囊按钮信息
                const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

                // 计算导航栏各项尺寸 - 使用固定高度确保一致性
                const customNavHeight = 44; // 固定导航内容高度
                const navbarHeight = statusBarHeight + customNavHeight;

                // 计算胶囊按钮样式 - 精确对齐微信原生胶囊
                const capsuleStyle = {
                    height: menuButtonInfo.height,
                    top: 47, // 固定 top 值为 47，确保与微信胶囊对齐
                    left: systemInfo.screenWidth - menuButtonInfo.right, // 使用与右侧胶囊相同的边距
                    borderRadius: menuButtonInfo.height / 2,
                    btnHeight: menuButtonInfo.height - 4,
                    btnWidth: (menuButtonInfo.width - 6) / 2, // 减去分隔线和边距
                    dividerHeight: menuButtonInfo.height - 8
                };

                // 计算标题最大宽度（避免与胶囊按钮重叠）
                const titleMaxWidth = systemInfo.screenWidth - (capsuleStyle.left + menuButtonInfo.width + 24) * 2;

                // 更新数据
                this.setData({
                    statusBarHeight,
                    navbarHeight,
                    customNavHeight,
                    titleMaxWidth,
                    capsuleStyle
                });

                // 设置背景色
                this.setNavbarStyle();

                console.log('导航栏初始化完成:', {
                    statusBarHeight,
                    navbarHeight,
                    customNavHeight,
                    capsuleStyle,
                    titleMaxWidth,
                    menuButtonInfo,
                    screenWidth: systemInfo.screenWidth
                });

            } catch (error) {
                console.error('导航栏初始化失败:', error);
                // 使用默认值
                this.setData({
                    statusBarHeight: 20,
                    navbarHeight: 88,
                    customNavHeight: 44,
                    titleMaxWidth: 300
                });
            }
        },

        /**
         * 设置导航栏样式
         */
        setNavbarStyle() {
            const {
                background,
                color
            } = this.properties;

            // 这里可以根据需要设置更多样式
            if (background !== '#ffffff') {
                this.setData({
                    'capsuleStyle.background': background
                });
            }
        },

        /**
         * 返回上一页
         */
        goBack() {
            const {
                customBack,
                delta
            } = this.properties;

            if (customBack) {
                // 触发自定义返回事件
                this.triggerEvent('back', {
                    delta
                });
                return;
            }

            // 检查页面栈
            const pages = getCurrentPages();

            if (pages.length <= 1) {
                // 如果只有一个页面，跳转到首页
                this.goHome();
                return;
            }

            // 正常返回
            router.navigateBack(delta);

            // 触发返回事件
            this.triggerEvent('back', {
                delta
            });
        },

        /**
         * 回到首页
         */
        goHome() {
            const {
                homePath
            } = this.properties;

            // 检查是否已经在首页
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];

            if (currentPage && currentPage.route === homePath.substring(1)) {
                console.log('已经在首页');
                return;
            }

            // 判断首页是否为 tabBar 页面
            if (this.isTabBarPage(homePath)) {
                // 使用路由名称而不是路径
                router.switchTab('gameList', {
                    fail: (err) => {
                        console.error('跳转首页失败:', err);
                        // 备用方案
                        router.reLaunch('gameList');
                    }
                });
            } else {
                router.reLaunch(homePath);
            }

            // 触发首页事件
            this.triggerEvent('home', {
                path: homePath
            });
        },

        /**
         * 判断是否为 tabBar 页面
         */
        isTabBarPage(path) {
            // 这里可以根据 app.json 中的 tabBar 配置来判断
            const tabBarPages = [
                '/pages/gameList/index',
                '/pages/history/index'
            ];

            return tabBarPages.includes(path);
        },

        /**
         * 获取导航栏高度（供外部调用）
         */
        getNavbarHeight() {
            return this.data.navbarHeight;
        }
    }
});