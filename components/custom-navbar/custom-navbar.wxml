<!--components/custom-navbar/custom-navbar.wxml-->
<view class="custom-navbar-box" style="height: {{navbarHeight}}px;">
    <!-- 状态栏占位 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    
    <!-- 导航栏内容 -->
    <view class="custom-navbar-content" style="height: {{customNavHeight}}px;">
      <!-- 左侧胶囊按钮组 -->
      <view 
        class="custom-nav-capsule" 
        style="height: {{capsuleStyle.height}}px; top: {{capsuleStyle.top}}px; left: {{capsuleStyle.left}}px; border-radius: {{capsuleStyle.borderRadius}}px;"
        wx:if="{{showCapsule}}"
      >
        <!-- 返回按钮 -->
        <view 
          class="capsule-btn back-btn" 
          style="height: {{capsuleStyle.btnHeight}}px; width: {{capsuleStyle.btnWidth}}px;"
          bindtap="goBack"
          wx:if="{{showBack}}"
        >
          <text class="btn-icon back-icon">‹</text>
        </view>
        
        <!-- 分隔线 -->
        <view 
          class="capsule-divider" 
          style="height: {{capsuleStyle.dividerHeight}}px;"
          wx:if="{{showBack && showHome}}"
        ></view>
        
        <!-- 首页按钮 -->
        <view 
          class="capsule-btn home-btn" 
          style="height: {{capsuleStyle.btnHeight}}px; width: {{capsuleStyle.btnWidth}}px;"
          bindtap="goHome"
          wx:if="{{showHome}}"
        >
          <text class="btn-icon home-icon">⌂</text>
        </view>
      </view>
      
      <!-- 标题 -->
      <view 
        class="nav-title" 
        style="height: {{customNavHeight}}px; line-height: {{customNavHeight}}px; max-width: {{titleMaxWidth}}px;"
        wx:if="{{title}}"
      >
        {{title}}
      </view>
      
      <!-- 右侧插槽 -->
      <view class="nav-right" wx:if="{{showRightSlot}}">
        <slot name="right"></slot>
      </view>
    </view>
</view>
