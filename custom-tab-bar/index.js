Component({
    data: {
        selected: '0',
        list: [{
            pagePath: "/pages/gameList/index",
            text: "游戏",
            value: "0"
        }, {
            pagePath: "/pages/history/index",
            text: "历史",
            value: "1"
        }]
    },
    attached() {
        // 延迟执行，确保页面已经加载完成
        setTimeout(() => {
            this.setData({
                selected: this.getTabIndex()
            });
        }, 100);
    },
    methods: {
        handleChange(e) {
            const value = e.detail.value;
            const index = parseInt(value);
            const path = this.data.list[index].pagePath;

            // 判断当前页面是否已经是目标页面，避免重复跳转
            const pages = getCurrentPages();
            if (pages && pages.length > 0) {
                const currentPage = pages[pages.length - 1];
                if (currentPage && currentPage.route) {
                    const currentUrl = '/' + currentPage.route;

                    if (currentUrl !== path) {
                        wx.switchTab({
                            url: path
                        });
                    }
                    return;
                }
            }

            // 如果无法获取当前页面信息，直接跳转
            wx.switchTab({
                url: path
            });
        },
        getTabIndex() {
            const pages = getCurrentPages();
            // 检查pages数组是否存在且不为空
            if (!pages || pages.length === 0) {
                return '0';
            }

            const currentPage = pages[pages.length - 1];
            // 检查currentPage是否存在且有route属性
            if (!currentPage || !currentPage.route) {
                return '0';
            }

            const url = '/' + currentPage.route;

            const tabList = this.data.list;
            for (let i = 0; i < tabList.length; i++) {
                if (tabList[i].pagePath === url) {
                    return i.toString();
                }
            }
            return '0';
        }
    }
})