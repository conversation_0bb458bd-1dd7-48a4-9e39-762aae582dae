{"swagger": "2.0", "info": {"description": "API for the Score Subtotal application", "title": "Score Subtotal API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/v1", "paths": {"/api/v1/auth/login": {"post": {"description": "微信小程序用户登录，使用code换取token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "用户登录", "parameters": [{"description": "登录请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.LoginRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.LoginResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/auth/refresh": {"post": {"description": "使用旧token获取新的访问token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "刷新token", "parameters": [{"description": "刷新token请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TokenRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.TokenResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "Token无效或已过期", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/games": {"get": {"security": [{"Bearer": []}], "description": "获取当前用户参与的所有游戏列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["游戏"], "summary": "获取游戏列表", "parameters": [{"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认为10", "name": "size", "in": "query"}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.GameListResponse"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}, "post": {"security": [{"Bearer": []}], "description": "创建一个新的游戏", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["游戏"], "summary": "创建游戏", "parameters": [{"description": "创建游戏请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateGameRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.Game"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/games/{gameId}": {"get": {"security": [{"Bearer": []}], "description": "获取单个游戏的详细信息，包括回合和分数", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["游戏"], "summary": "获取游戏详情", "parameters": [{"type": "integer", "description": "游戏ID", "name": "gameId", "in": "path", "required": true}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "游戏不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除一个游戏（仅创建者可操作）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["游戏"], "summary": "删除游戏", "parameters": [{"type": "integer", "description": "游戏ID", "name": "gameId", "in": "path", "required": true}], "responses": {"200": {"description": "成功响应", "schema": {"$ref": "#/definitions/utils.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "游戏不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/games/{gameId}/rounds/{roundId}": {"delete": {"security": [{"Bearer": []}], "description": "删除游戏中的一个回合及其分数", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分数"], "summary": "删除回合", "parameters": [{"type": "integer", "description": "游戏ID", "name": "gameId", "in": "path", "required": true}, {"type": "integer", "description": "回合ID", "name": "roundId", "in": "path", "required": true}], "responses": {"200": {"description": "成功响应，返回更新后的玩家列表", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.Player"}}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "游戏或回合不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/games/{gameId}/scores": {"post": {"security": [{"Bearer": []}], "description": "为游戏添加新回合并提交玩家分数", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分数"], "summary": "提交回合分数", "parameters": [{"type": "integer", "description": "游戏ID", "name": "gameId", "in": "path", "required": true}, {"description": "分数提交请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SubmitScoreRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "游戏不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/games/{gameId}/settle": {"post": {"security": [{"Bearer": []}], "description": "结算游戏并生成结算结果", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["游戏"], "summary": "结算游戏", "parameters": [{"type": "integer", "description": "游戏ID", "name": "gameId", "in": "path", "required": true}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "游戏不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/invites": {"post": {"security": [{"Bearer": []}], "description": "创建一个新的游戏邀请", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请"], "summary": "创建邀请", "parameters": [{"description": "创建邀请请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateInviteRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.Invite"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/invites/{inviteId}": {"get": {"security": [{"Bearer": []}], "description": "获取邀请的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请"], "summary": "获取邀请信息", "parameters": [{"type": "integer", "description": "邀请ID", "name": "inviteId", "in": "path", "required": true}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.InviteInfo"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "邀请不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/invites/{inviteId}/join": {"post": {"security": [{"Bearer": []}], "description": "加入一个游戏邀请", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请"], "summary": "加入邀请", "parameters": [{"type": "integer", "description": "邀请ID", "name": "inviteId", "in": "path", "required": true}, {"description": "加入邀请请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.JoinInviteRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.JoinInviteResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "邀请不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/invites/{inviteId}/start": {"post": {"security": [{"Bearer": []}], "description": "从邀请创建并开始一个新游戏", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邀请"], "summary": "开始邀请游戏", "parameters": [{"type": "integer", "description": "邀请ID", "name": "inviteId", "in": "path", "required": true}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.Game"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/utils.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "403": {"description": "权限不足，只有创建者可以开始游戏", "schema": {"$ref": "#/definitions/utils.Response"}}, "404": {"description": "邀请不存在", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/stats/history": {"get": {"security": [{"Bearer": []}], "description": "获取用户的游戏历史统计数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["统计"], "summary": "获取游戏历史统计", "parameters": [{"type": "string", "description": "游戏类型", "name": "gameType", "in": "query"}, {"type": "string", "description": "时间范围，默认为all", "name": "timeRange", "in": "query"}], "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/stats/user": {"get": {"security": [{"Bearer": []}], "description": "获取当前用户的游戏统计数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["统计"], "summary": "获取用户统计", "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/system/config": {"get": {"description": "获取应用的系统配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统"], "summary": "获取系统配置", "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}, "/api/v1/user/info": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "获取用户信息", "responses": {"200": {"description": "成功响应", "schema": {"allOf": [{"$ref": "#/definitions/utils.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.UserInfo"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/utils.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/utils.Response"}}}}}}, "definitions": {"models.CreateGameRequest": {"type": "object", "required": ["initialScore", "mode", "name", "playerCount", "players", "type"], "properties": {"initialScore": {"type": "integer"}, "mode": {"type": "string"}, "name": {"type": "string"}, "playerCount": {"type": "integer"}, "players": {"type": "array", "items": {"$ref": "#/definitions/models.PlayerRequest"}}, "type": {"type": "string"}}}, "models.CreateInviteRequest": {"type": "object", "required": ["gameInfo"], "properties": {"gameInfo": {"$ref": "#/definitions/models.GameInfo"}}}, "models.Game": {"type": "object", "properties": {"createTime": {"type": "string"}, "id": {"type": "integer"}, "initialScore": {"type": "integer"}, "mode": {"type": "string"}, "name": {"type": "string"}, "playerCount": {"type": "integer"}, "players": {"type": "array", "items": {"$ref": "#/definitions/models.Player"}}, "rounds": {"type": "array", "items": {"$ref": "#/definitions/models.Round"}}, "settleTime": {"type": "string"}, "settled": {"type": "boolean"}, "type": {"type": "string"}, "updateTime": {"type": "string"}, "userId": {"type": "integer"}}}, "models.GameInfo": {"type": "object", "required": ["initialScore", "name", "playerCount", "type"], "properties": {"initialScore": {"type": "integer"}, "name": {"type": "string"}, "playerCount": {"type": "integer"}, "type": {"type": "string"}}}, "models.GameListItem": {"type": "object", "properties": {"createTime": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "playerCount": {"type": "integer"}, "settled": {"type": "boolean"}, "type": {"type": "string"}}}, "models.GameListResponse": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/models.GameListItem"}}, "total": {"type": "integer"}}}, "models.Invite": {"type": "object", "properties": {"createTime": {"type": "string"}, "expireTime": {"type": "string"}, "gameName": {"type": "string"}, "gameType": {"type": "string"}, "id": {"type": "integer"}, "initialScore": {"type": "integer"}, "playerCount": {"type": "integer"}, "players": {"type": "array", "items": {"$ref": "#/definitions/models.InvitePlayer"}}, "status": {"description": "waiting, started, ended", "type": "string"}, "updateTime": {"type": "string"}, "userId": {"type": "integer"}}}, "models.InviteInfo": {"type": "object", "properties": {"gameInfo": {"$ref": "#/definitions/models.GameInfo"}, "id": {"type": "integer"}, "joinedCount": {"type": "integer"}, "players": {"type": "array", "items": {"$ref": "#/definitions/models.InvitePlayerInfo"}}, "status": {"type": "string"}}}, "models.InvitePlayer": {"type": "object", "properties": {"avatar": {"type": "string"}, "id": {"type": "integer"}, "inviteId": {"type": "integer"}, "joinTime": {"type": "string"}, "name": {"type": "string"}, "status": {"description": "joined, ready, offline", "type": "string"}, "userId": {"type": "integer"}}}, "models.InvitePlayerInfo": {"type": "object", "properties": {"avatar": {"type": "string"}, "id": {"type": "integer"}, "joinTime": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string"}}}, "models.JoinInviteRequest": {"type": "object", "required": ["avatar", "name"], "properties": {"avatar": {"type": "string"}, "name": {"type": "string"}}}, "models.JoinInviteResponse": {"type": "object", "properties": {"inviteInfo": {"$ref": "#/definitions/models.InviteInfo"}, "joinStatus": {"description": "success, full, expired", "type": "string"}, "playerId": {"type": "string"}}}, "models.LoginRequest": {"type": "object", "required": ["code", "userInfo"], "properties": {"code": {"type": "string"}, "userInfo": {"$ref": "#/definitions/models.UserInfo"}}}, "models.LoginResponse": {"type": "object", "properties": {"expireTime": {"type": "integer"}, "openid": {"type": "string"}, "token": {"type": "string"}, "userInfo": {"$ref": "#/definitions/models.UserInfo"}}}, "models.Player": {"type": "object", "properties": {"avatar": {"type": "string"}, "color": {"type": "string"}, "gameId": {"type": "integer"}, "id": {"type": "integer"}, "isOwner": {"type": "boolean"}, "name": {"type": "string"}, "score": {"type": "integer"}, "userId": {"type": "integer"}}}, "models.PlayerRequest": {"type": "object", "required": ["color", "name"], "properties": {"color": {"type": "string"}, "name": {"type": "string"}}}, "models.Round": {"type": "object", "properties": {"gameId": {"type": "integer"}, "id": {"type": "integer"}, "scores": {"type": "array", "items": {"$ref": "#/definitions/models.Score"}}, "time": {"type": "string"}}}, "models.Score": {"type": "object", "properties": {"id": {"type": "integer"}, "playerId": {"type": "integer"}, "score": {"type": "integer"}}}, "models.ScoreRequest": {"type": "object", "required": ["playerId", "score"], "properties": {"playerId": {"type": "integer"}, "score": {"type": "integer"}}}, "models.SubmitScoreRequest": {"type": "object", "required": ["scores"], "properties": {"scores": {"type": "array", "items": {"$ref": "#/definitions/models.ScoreRequest"}}}}, "models.TokenRequest": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}, "models.TokenResponse": {"type": "object", "properties": {"expireTime": {"type": "integer"}, "token": {"type": "string"}}}, "models.UserInfo": {"type": "object", "properties": {"avatarUrl": {"type": "string"}, "gender": {"type": "integer"}, "nickName": {"type": "string"}}}, "utils.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}}, "securityDefinitions": {"Bearer": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}