# 计分小程序 API 文档

本文档列出了计分小程序前端与后端交互所需的所有API接口。

## 基础信息

- **基础URL**: `https://api.scoreapp.com/v1`
- **数据格式**: 所有请求和响应均使用JSON格式
- **认证方式**: 小程序通过 `Authorization` 头传递 token

```
Authorization: Bearer {token}
```

## 错误处理

所有API在发生错误时返回统一格式：

```json
{
  "code": 400,
  "message": "错误描述信息",
  "data": null
}
```

常见错误码：
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

## 1. 用户模块

### 1.1 小程序登录

通过微信 code 登录获取用户信息和 token

**请求**:
```
POST /auth/login
```

**参数**:

```json
{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "用户头像URL",
    "gender": 1
  }
}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "JWT令牌",
    "openid": "用户openid",
    "userInfo": {
      "id": "用户ID",
      "nickName": "用户昵称",
      "avatarUrl": "用户头像URL",
      "gender": 1
    },
    "expireTime": 1634567890000
  }
}
```

### 1.2 刷新令牌

**请求**:
```
POST /auth/refresh
```

**参数**:

```json
{
  "token": "当前token"
}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "新JWT令牌",
    "expireTime": 1634567890000
  }
}
```

### 1.3 获取用户信息

**请求**:
```
GET /user/info
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "用户ID",
    "nickName": "用户昵称",
    "avatarUrl": "用户头像URL",
    "gender": 1,
    "gameCount": 10,
    "winCount": 5
  }
}
```

## 2. 游戏模块

### 2.1 创建游戏

**请求**:
```
POST /games
```

**参数**:

```json
{
  "name": "游戏名称",
  "type": "mahjong", // mahjong, poker, board, other
  "mode": "local", // local, invite
  "playerCount": 4,
  "initialScore": 1000,
  "players": [
    {
      "name": "玩家1",
      "color": "#1296db"
    },
    {
      "name": "玩家2",
      "color": "#52c41a"
    }
  ]
}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "游戏ID",
    "name": "游戏名称",
    "type": "mahjong",
    "mode": "local",
    "playerCount": 4,
    "initialScore": 1000,
    "players": [
      {
        "id": "玩家ID",
        "name": "玩家1",
        "color": "#1296db",
        "score": 1000
      },
      {
        "id": "玩家ID",
        "name": "玩家2",
        "color": "#52c41a",
        "score": 1000
      }
    ],
    "rounds": [],
    "createTime": 1634567890000,
    "updateTime": 1634567890000
  }
}
```

### 2.2 获取游戏列表

**请求**:
```
GET /games
```

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页条数，默认10

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 23,
    "list": [
      {
        "id": "游戏ID",
        "name": "游戏名称",
        "type": "mahjong",
        "playerCount": 4,
        "createTime": 1634567890000,
        "settled": false
      }
    ]
  }
}
```

### 2.3 获取游戏详情

**请求**:
```
GET /games/{gameId}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "游戏ID",
    "name": "游戏名称",
    "type": "mahjong",
    "mode": "local",
    "playerCount": 4,
    "initialScore": 1000,
    "players": [
      {
        "id": "玩家ID",
        "name": "玩家1",
        "color": "#1296db",
        "score": 1200
      },
      {
        "id": "玩家ID",
        "name": "玩家2",
        "color": "#52c41a",
        "score": 800
      }
    ],
    "rounds": [
      {
        "id": "轮次ID",
        "time": 1634567890000,
        "scores": [
          {
            "playerId": "玩家ID",
            "score": 200
          },
          {
            "playerId": "玩家ID",
            "score": -200
          }
        ]
      }
    ],
    "createTime": 1634567890000,
    "updateTime": 1634567890000,
    "settled": false
  }
}
```

### 2.4 删除游戏

**请求**:
```
DELETE /games/{gameId}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 3. 记分模块

### 3.1 提交得分

**请求**:
```
POST /games/{gameId}/scores
```

**参数**:

```json
{
  "scores": [
    {
      "playerId": "玩家ID",
      "score": 200
    },
    {
      "playerId": "玩家ID",
      "score": -200
    }
  ]
}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "roundId": "轮次ID",
    "time": 1634567890000,
    "players": [
      {
        "id": "玩家ID",
        "name": "玩家1",
        "score": 1200,
        "change": 200
      },
      {
        "id": "玩家ID",
        "name": "玩家2",
        "score": 800,
        "change": -200
      }
    ]
  }
}
```

### 3.2 删除轮次得分

**请求**:
```
DELETE /games/{gameId}/rounds/{roundId}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "players": [
      {
        "id": "玩家ID",
        "name": "玩家1",
        "score": 1000
      },
      {
        "id": "玩家ID",
        "name": "玩家2",
        "score": 1000
      }
    ]
  }
}
```

### 3.3 结算游戏

**请求**:
```
POST /games/{gameId}/settle
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "rankedPlayers": [
      {
        "id": "玩家ID",
        "name": "玩家1",
        "score": 1200,
        "rank": 1
      },
      {
        "id": "玩家ID",
        "name": "玩家2",
        "score": 800,
        "rank": 2
      }
    ],
    "payments": [
      {
        "from": "玩家ID",
        "to": "玩家ID",
        "amount": 200
      }
    ]
  }
}
```

## 4. 邀请模块

### 4.1 创建邀请

**请求**:
```
POST /invites
```

**参数**:

```json
{
  "gameInfo": {
    "name": "游戏名称",
    "type": "mahjong",
    "playerCount": 4,
    "initialScore": 1000
  }
}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "inviteId": "邀请ID",
    "qrCodeUrl": "小程序码URL",
    "inviteLink": "邀请链接",
    "expireTime": 1634567890000
  }
}
```

### 4.2 获取邀请信息

**请求**:
```
GET /invites/{inviteId}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "邀请ID",
    "gameInfo": {
      "name": "游戏名称",
      "type": "mahjong",
      "playerCount": 4,
      "initialScore": 1000
    },
    "players": [
      {
        "id": "玩家ID",
        "name": "玩家1",
        "avatar": "头像URL",
        "joinTime": 1634567890000,
        "status": "joined" // joined, ready, offline
      }
    ],
    "status": "waiting", // waiting, started, ended
    "createTime": 1634567890000,
    "expireTime": 1634567890000
  }
}
```

### 4.3 加入游戏邀请

**请求**:
```
POST /invites/{inviteId}/join
```

**参数**:

```json
{
  "name": "玩家名称",
  "avatar": "头像URL"
}
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "playerId": "玩家ID",
    "joinStatus": "success", // success, full, expired
    "inviteInfo": {
      "id": "邀请ID",
      "gameInfo": {
        "name": "游戏名称",
        "type": "mahjong",
        "playerCount": 4,
        "initialScore": 1000
      },
      "players": [
        {
          "id": "玩家ID",
          "name": "玩家1",
          "avatar": "头像URL",
          "joinTime": 1634567890000,
          "status": "joined"
        }
      ],
      "joinedCount": 1,
      "status": "waiting"
    }
  }
}
```

### 4.4 开始邀请游戏

**请求**:
```
POST /invites/{inviteId}/start
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "gameId": "新创建的游戏ID",
    "players": [
      {
        "id": "玩家ID",
        "name": "玩家1",
        "avatar": "头像URL",
        "score": 1000
      }
    ]
  }
}
```

## 5. 数据统计

### 5.1 获取用户游戏统计

**请求**:
```
GET /stats/user
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalGames": 10,
    "winCount": 5,
    "winRate": 0.5,
    "totalScore": 2500,
    "avgScore": 250,
    "gameTypeStats": [
      {
        "type": "mahjong",
        "count": 6,
        "winCount": 3
      },
      {
        "type": "poker",
        "count": 4,
        "winCount": 2
      }
    ]
  }
}
```

### 5.2 获取游戏历史统计

**请求**:
```
GET /stats/history
```

**查询参数**:
- `type`: 游戏类型，可选
- `timeRange`: 时间范围，可选值：week, month, year, all

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "gameCount": 20,
    "gameByType": {
      "mahjong": 12,
      "poker": 8
    },
    "recentGames": [
      {
        "id": "游戏ID",
        "name": "游戏名称",
        "type": "mahjong",
        "createTime": 1634567890000,
        "playerCount": 4,
        "result": "win" // win, lose, draw
      }
    ]
  }
}
```

## 6. 文件上传

### 6.1 上传用户头像

**请求**:
```
POST /upload/avatar
```

**参数**:
- 文件表单数据

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "url": "头像URL"
  }
}
```

## 7. 系统配置

### 7.1 获取系统配置

**请求**:
```
GET /config
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "version": "1.0.0",
    "updateInfo": "更新内容",
    "gameTypes": [
      {
        "id": "mahjong",
        "name": "麻将",
        "icon": "🀄",
        "defaultScore": 1000
      },
      {
        "id": "poker",
        "name": "扑克",
        "icon": "🃏",
        "defaultScore": 500
      }
    ]
  }
}
```

## 8. 网络要求

1. 所有接口应使用HTTPS协议
2. 建议使用WebSocket保持实时连接，特别是在邀请游戏模式下
3. 考虑网络不稳定情况下的数据一致性
4. 考虑使用缓存策略减少请求次数
5. 在关键操作(如记分、删除)使用乐观锁或事务确保数据一致性

## 9. 安全要求

1. 使用JWT进行身份验证，定期刷新token
2. 敏感操作需要二次验证
3. 设置请求频率限制，防止恶意攻击
4. 数据传输过程中加密敏感信息
5. 服务端需做好输入验证，防止注入攻击 

## 10. 后端技术架构

### 10.1 技术栈

- **编程语言**: Go (Golang) 1.18+
- **Web框架**: Gin 1.8+
- **ORM框架**: GORM 1.24+
- **数据库**: MySQL 8.0
- **认证**: JWT
- **文档**: Swagger/OpenAPI 3.0
- **日志**: Zap
- **配置管理**: Viper
- **部署**: Docker + Docker Compose

### 10.2 项目目录结构

```
scoreapp/
├── cmd/                   # 应用程序入口点
│   └── api/               # API服务器入口
│       └── main.go        # 主程序
├── configs/               # 配置文件目录
│   └── config.yaml        # 应用配置文件
├── internal/              # 私有应用和库代码
│   ├── api/               # API层
│   │   ├── handlers/      # HTTP处理器
│   │   ├── middlewares/   # HTTP中间件
│   │   └── routes/        # 路由定义
│   ├── models/            # 数据模型
│   ├── repositories/      # 数据访问层
│   ├── services/          # 业务逻辑层
│   └── utils/             # 工具函数
├── pkg/                   # 可被外部应用程序使用的库代码
│   ├── auth/              # 认证相关
│   ├── logger/            # 日志模块
│   ├── config/            # 配置模块
│   └── wechat/            # 微信相关API
├── scripts/               # 构建、安装、分析等脚本
├── docs/                  # 文档
│   └── swagger/           # Swagger文档
├── deployments/           # 部署配置文件(Docker Compose等)
├── .env.example           # 环境变量示例
├── go.mod                 # Go模块定义
├── go.sum                 # Go模块校验和
├── Makefile               # Make工具配置
└── README.md              # 项目说明文档
```

### 10.3 分层架构

项目采用清晰的分层架构，遵循依赖倒置原则:

1. **API层(Handlers)**: 处理HTTP请求，参数验证，响应格式化
2. **服务层(Services)**: 实现业务逻辑，调用存储库
3. **存储库层(Repositories)**: 封装数据访问逻辑，与数据库交互
4. **模型层(Models)**: 定义数据结构和领域模型

依赖关系: Handler -> Service -> Repository -> Model

### 10.4 主要包详解

#### 10.4.1 models

定义数据库映射模型和业务模型:

```go
// internal/models/user.go
package models

import (
    "time"
    "gorm.io/gorm"
)

// User 用户表
type User struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"createdAt"`
    UpdatedAt time.Time      `json:"updatedAt"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
    
    OpenID    string `gorm:"uniqueIndex;size:32" json:"openId"`
    NickName  string `gorm:"size:64" json:"nickName"`
    AvatarURL string `gorm:"size:255" json:"avatarUrl"`
    Gender    int    `json:"gender"`
}

// Game 游戏表
type Game struct {
    ID           uint           `gorm:"primarykey" json:"id"`
    CreatedAt    time.Time      `json:"createdAt"`
    UpdatedAt    time.Time      `json:"updatedAt"`
    DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
    
    Name         string    `gorm:"size:64" json:"name"`
    Type         string    `gorm:"size:16" json:"type"`
    Mode         string    `gorm:"size:16" json:"mode"`
    PlayerCount  int       `json:"playerCount"`
    InitialScore int       `json:"initialScore"`
    UserID       uint      `json:"userId"`
    Settled      bool      `json:"settled"`
    SettleTime   time.Time `json:"settleTime"`
}

// 其他模型定义...
```

#### 10.4.2 repositories

封装数据访问逻辑:

```go
// internal/repositories/user_repository.go
package repositories

import (
    "context"
    "github.com/your-username/scoreapp/internal/models"
    "gorm.io/gorm"
)

type UserRepository interface {
    Create(ctx context.Context, user *models.User) error
    FindByOpenID(ctx context.Context, openID string) (*models.User, error)
    // 其他方法...
}

type userRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
    return &userRepository{
        db: db,
    }
}

func (r *userRepository) Create(ctx context.Context, user *models.User) error {
    return r.db.WithContext(ctx).Create(user).Error
}

func (r *userRepository) FindByOpenID(ctx context.Context, openID string) (*models.User, error) {
    var user models.User
    err := r.db.WithContext(ctx).Where("open_id = ?", openID).First(&user).Error
    if err != nil {
        return nil, err
    }
    return &user, nil
}

// 其他实现方法...
```

#### 10.4.3 services

实现业务逻辑:

```go
// internal/services/auth_service.go
package services

import (
    "context"
    "github.com/your-username/scoreapp/internal/models"
    "github.com/your-username/scoreapp/internal/repositories"
    "github.com/your-username/scoreapp/pkg/auth"
    "github.com/your-username/scoreapp/pkg/wechat"
)

type AuthService interface {
    Login(ctx context.Context, code string, userInfo models.UserInfo) (*models.LoginResponse, error)
    RefreshToken(ctx context.Context, token string) (*models.TokenResponse, error)
}

type authService struct {
    userRepo repositories.UserRepository
    wechat   wechat.WechatClient
    jwt      auth.JWTService
}

func NewAuthService(
    userRepo repositories.UserRepository,
    wechat wechat.WechatClient,
    jwt auth.JWTService,
) AuthService {
    return &authService{
        userRepo: userRepo,
        wechat:   wechat,
        jwt:      jwt,
    }
}

func (s *authService) Login(ctx context.Context, code string, userInfo models.UserInfo) (*models.LoginResponse, error) {
    // 实现登录逻辑...
}

// 其他实现方法...
```

#### 10.4.4 handlers

处理HTTP请求:

```go
// internal/api/handlers/auth_handler.go
package handlers

import (
    "github.com/gin-gonic/gin"
    "github.com/your-username/scoreapp/internal/models"
    "github.com/your-username/scoreapp/internal/services"
    "net/http"
)

type AuthHandler struct {
    authService services.AuthService
}

func NewAuthHandler(authService services.AuthService) *AuthHandler {
    return &AuthHandler{
        authService: authService,
    }
}

func (h *AuthHandler) Login(c *gin.Context) {
    var req models.LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            "code":    400,
            "message": "参数错误: " + err.Error(),
            "data":    nil,
        })
        return
    }
    
    resp, err := h.authService.Login(c.Request.Context(), req.Code, req.UserInfo)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            "code":    500,
            "message": "登录失败: " + err.Error(),
            "data":    nil,
        })
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "code":    200,
        "message": "success",
        "data":    resp,
    })
}

// 其他处理方法...
```

#### 10.4.5 routes

配置API路由:

```go
// internal/api/routes/routes.go
package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/your-username/scoreapp/internal/api/handlers"
    "github.com/your-username/scoreapp/internal/api/middlewares"
)

func SetupRoutes(
    r *gin.Engine,
    authHandler *handlers.AuthHandler,
    gameHandler *handlers.GameHandler,
    // 其他handler...
    authMiddleware middlewares.AuthMiddleware,
) {
    // 无需认证的路由
    public := r.Group("/api/v1")
    {
        auth := public.Group("/auth")
        {
            auth.POST("/login", authHandler.Login)
            auth.POST("/refresh", authHandler.RefreshToken)
        }
    }
    
    // 需要认证的路由
    private := r.Group("/api/v1")
    private.Use(authMiddleware.VerifyToken())
    {
        // 用户相关
        user := private.Group("/user")
        {
            user.GET("/info", userHandler.GetUserInfo)
        }
        
        // 游戏相关
        games := private.Group("/games")
        {
            games.POST("", gameHandler.CreateGame)
            games.GET("", gameHandler.GetGameList)
            games.GET("/:gameId", gameHandler.GetGameDetail)
            games.DELETE("/:gameId", gameHandler.DeleteGame)
            
            // 记分相关
            games.POST("/:gameId/scores", scoreHandler.SubmitScores)
            games.DELETE("/:gameId/rounds/:roundId", scoreHandler.DeleteRound)
            games.POST("/:gameId/settle", gameHandler.SettleGame)
        }
        
        // 邀请相关
        invites := private.Group("/invites")
        {
            invites.POST("", inviteHandler.CreateInvite)
            invites.GET("/:inviteId", inviteHandler.GetInviteInfo)
            invites.POST("/:inviteId/join", inviteHandler.JoinInvite)
            invites.POST("/:inviteId/start", inviteHandler.StartInviteGame)
        }
        
        // 统计相关
        stats := private.Group("/stats")
        {
            stats.GET("/user", statsHandler.GetUserStats)
            stats.GET("/history", statsHandler.GetHistoryStats)
        }
    }
}
```

### 10.5 数据库设计

主要表结构:

1. **users**: 用户信息表
2. **games**: 游戏信息表
3. **players**: 玩家信息表
4. **rounds**: 游戏轮次表
5. **scores**: 玩家得分表
6. **invites**: 游戏邀请表

#### 10.5.1 主要表结构

```sql
-- 用户表
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `open_id` varchar(32) NOT NULL,
  `nick_name` varchar(64) DEFAULT NULL,
  `avatar_url` varchar(255) DEFAULT NULL,
  `gender` tinyint(4) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 游戏表
CREATE TABLE `games` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `name` varchar(64) NOT NULL,
  `type` varchar(16) NOT NULL,
  `mode` varchar(16) NOT NULL,
  `player_count` int(11) NOT NULL,
  `initial_score` int(11) NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `settled` tinyint(1) DEFAULT 0,
  `settle_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 其他表结构...
```

### 10.6 开发规范

#### 10.6.1 错误处理

统一使用 `errors` 包处理错误，定义自定义错误类型：

```go
// pkg/errors/errors.go
package errors

import (
    "fmt"
    "net/http"
)

// AppError 应用错误类型
type AppError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
}

func (e AppError) Error() string {
    return e.Message
}

// 预定义错误
var (
    ErrInvalidParam = AppError{Code: http.StatusBadRequest, Message: "无效参数"}
    ErrUnauthorized = AppError{Code: http.StatusUnauthorized, Message: "未授权操作"}
    ErrNotFound     = AppError{Code: http.StatusNotFound, Message: "资源不存在"}
    // 更多错误类型...
)

// WithMessage 为错误添加额外信息
func WithMessage(err error, message string) error {
    if appErr, ok := err.(AppError); ok {
        return AppError{
            Code:    appErr.Code,
            Message: fmt.Sprintf("%s: %s", appErr.Message, message),
        }
    }
    return err
}
```

#### 10.6.2 日志规范

使用结构化日志，通过 Zap 实现：

```go
// pkg/logger/logger.go
package logger

import (
    "go.uber.org/zap"
    "go.uber.org/zap/zapcore"
)

var log *zap.Logger

// 初始化日志
func Init(level string, production bool) {
    // 日志配置...
}

// 日志方法
func Info(msg string, fields ...zap.Field) {
    log.Info(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
    log.Error(msg, fields...)
}

// 更多日志方法...
```

#### 10.6.3 配置管理

使用 Viper 管理配置：

```go
// pkg/config/config.go
package config

import (
    "github.com/spf13/viper"
)

type Config struct {
    Server   ServerConfig
    Database DatabaseConfig
    Redis    RedisConfig
    Wechat   WechatConfig
    JWT      JWTConfig
}

type ServerConfig struct {
    Port int
    Mode string
}

// 其他配置结构...

func LoadConfig(path string) (*Config, error) {
    // 配置加载逻辑...
}
```

### 10.7 部署方案

使用 Docker 和 Docker Compose 进行容器化部署:

```yaml
# deployments/docker-compose.yml
version: '3.8'

services:
  api:
    build:
      context: ..
      dockerfile: deployments/Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - db
      - redis
    environment:
      - DB_HOST=db
      - DB_PORT=3306
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      # 其他环境变量...
    volumes:
      - ../configs:/app/configs
    restart: always

  db:
    image: mysql:8.0
    volumes:
      - mysql_data:/var/lib/mysql
      - ../scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=scoreapp
    ports:
      - "3306:3306"
    restart: always

  redis:
    image: redis:6.0-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: always

volumes:
  mysql_data:
  redis_data:
```

### 10.8 开发流程

1. **设计API**: 使用Swagger/OpenAPI定义API规范
2. **数据库迁移**: 使用GORM自动迁移或手动SQL脚本
3. **功能开发**: 按照领域模块开发功能
4. **单元测试**: 针对服务层和存储库层编写测试
5. **接口测试**: 使用Postman或其他工具测试API
6. **代码审查**: 通过Pull Request进行代码审查
7. **CI/CD**: 使用GitHub Actions等工具自动构建和部署 