# 微信小程序前端开发指南

本文档收集了微信小程序前端开发中常用的组件实现方法，方便快速复用。

## 目录
1. [底部导航栏 - TDesign实现](#底部导航栏---tdesign实现)
2. [待添加...](#待添加)

---

<a id="底部导航栏---tdesign实现"></a>
## 1. 底部导航栏 - TDesign实现

### 实现目标
使用TDesign组件库实现自定义底部导航栏，支持图标切换、徽标显示等功能。

### 代码片段

#### 安装TDesign组件库
```bash
# 在项目根目录执行
npm i tdesign-miniprogram -S --production

# 如果项目没有package.json文件，可以先初始化
npm init -y
```

#### app.json配置
```json
{
  "tabBar": {
    "custom": true,
    "list": [
      {
        "pagePath": "pages/gameList/index",
        "text": "游戏"
      },
      {
        "pagePath": "pages/history/index",
        "text": "历史"
      }
    ]
  },
  "usingComponents": {
    "t-toast": "tdesign-miniprogram/toast/toast"
  }
}
```

#### custom-tab-bar/index.json
```json
{
  "component": true,
  "usingComponents": {
    "t-tab-bar": "tdesign-miniprogram/tab-bar/tab-bar",
    "t-tab-bar-item": "tdesign-miniprogram/tab-bar-item/tab-bar-item"
  }
}
```

#### custom-tab-bar/index.wxml
```html
<t-tab-bar value="{{ selected }}" theme="tag" split="{{ false }}" bind:change="handleChange">
  <t-tab-bar-item icon="app" value="0">游戏</t-tab-bar-item>
  <t-tab-bar-item icon="chart" value="1">历史</t-tab-bar-item>
</t-tab-bar>
```

#### custom-tab-bar/index.js
```javascript
Component({
  data: {
    selected: '0',
    list: [{
      pagePath: "/pages/gameList/index",
      text: "游戏",
      value: "0"
    }, {
      pagePath: "/pages/history/index",
      text: "历史",
      value: "1"
    }]
  },
  attached() {
    // 延迟执行，确保页面已经加载完成
    setTimeout(() => {
      this.setData({
        selected: this.getTabIndex()
      });
    }, 100);
  },
  methods: {
    handleChange(e) {
      const value = e.detail.value;
      const index = parseInt(value);
      const path = this.data.list[index].pagePath;
      
      // 判断当前页面是否已经是目标页面，避免重复跳转
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.route) {
          const currentUrl = '/' + currentPage.route;
          
          if (currentUrl !== path) {
            wx.switchTab({
              url: path
            });
          }
          return;
        }
      }
      
      // 如果无法获取当前页面信息，直接跳转
      wx.switchTab({
        url: path
      });
    },
    getTabIndex() {
      const pages = getCurrentPages();
      // 检查pages数组是否存在且不为空
      if (!pages || pages.length === 0) {
        return '0';
      }
      
      const currentPage = pages[pages.length - 1];
      // 检查currentPage是否存在且有route属性
      if (!currentPage || !currentPage.route) {
        return '0';
      }
      
      const url = '/' + currentPage.route;
      
      const tabList = this.data.list;
      for (let i = 0; i < tabList.length; i++) {
        if (tabList[i].pagePath === url) {
          return i.toString();
        }
      }
      return '0';
    }
  }
})
```

#### custom-tab-bar/index.wxss
```css
/* TDesign组件会自动应用样式 */
```

### 构建与使用

#### 构建npm包
```bash
# 使用开发者工具的npm构建功能
# 或手动构建:
mkdir -p miniprogram_npm/tdesign-miniprogram
cp -r node_modules/tdesign-miniprogram/miniprogram_dist/* miniprogram_npm/tdesign-miniprogram/
```

#### 图标配置
TDesign提供了丰富的图标，可以在t-tab-bar-item的icon属性中指定：
- app - 应用图标
- chat - 聊天图标
- user - 用户图标
- home - 首页图标
- chart - 图表图标

#### 徽标显示
```html
<t-tab-bar-item icon="chat" value="1" badge-props="{{ { count: unreadNum } }}">消息</t-tab-bar-item>
```

#### 主题设置
```html
<t-tab-bar value="{{ selected }}" theme="card" split="{{ false }}" bind:change="handleChange">
  <!-- 内容 -->
</t-tab-bar>
```

#### 页面中更新选中状态
```javascript
onShow() {
  if (typeof this.getTabBar === 'function' && this.getTabBar()) {
    this.getTabBar().setData({
      selected: '0'  // 根据当前页面设置对应的索引
    });
  }
}
```

### 注意事项
1. 确保app.json中的tabBar.list中的pagePath与custom-tab-bar/index.js中的list配置一致
2. 图标可以根据需要自定义，TDesign提供了丰富的图标库
3. 使用自定义tabBar时，必须将app.json中的tabBar.custom设置为true
4. 如果组件无法显示，检查miniprogram_npm目录是否正确构建

<a id="待添加"></a>
## 2. 待添加
此处将添加更多微信小程序前端开发的组件实现方法。 