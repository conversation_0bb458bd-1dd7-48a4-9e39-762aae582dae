import{__rest}from"tslib";import{getInstance}from"../common/utils";export var ActionSheetTheme;!function(t){t.List="list",t.Grid="grid"}(ActionSheetTheme||(ActionSheetTheme={}));export const show=function(t){const e=Object.assign({},t),{context:o,selector:n="#t-action-sheet"}=e,c=__rest(e,["context","selector"]),s=getInstance(o,n);if(s)return s.show(Object.assign({},c)),s;console.error("未找到组件,请确认 selector && context 是否正确")};export const close=function(t){const{context:e,selector:o="#t-action-sheet"}=Object.assign({},t),n=getInstance(e,o);n&&n.close()};