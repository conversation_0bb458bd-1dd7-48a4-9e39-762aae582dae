<import src="../common/template/icon.wxml"/><wxs src="../common/utils.wxs" module="_"/><wxs src="./avatar.wxs" module="_this"/><view class="{{classPrefix}}__wrapper class {{prefix}}-class" style="{{_._style([_this.getStyles(isShow), style, customStyle])}}"><t-badge color="{{badgeProps.color || ''}}" content="{{badgeProps.content || ''}}" count="{{badgeProps.count || 0}}" dot="{{badgeProps.dot || false}}" max-count="{{badgeProps.maxCount || 99}}" offset="{{badgeProps.offset || []}}" shape="{{badgeProps.shape || 'circle'}}" show-zero="{{badgeProps.showZero || false}}" size="{{badgeProps.size || 'medium'}}" t-class="{{badgeProps.tClass}}" t-class-content="{{badgeProps.tClassContent}}" t-class-count="{{badgeProps.tClassCount}}"><view class="{{_this.getClass(classPrefix, size || 'medium', shape, bordered)}} {{prefix}}-class-image" style="{{_this.getSize(size, systemInfo)}}" aria-label="{{ ariaLabel || alt ||'头像'}}" aria-role="{{ ariaRole || 'img'}}" aria-hidden="{{ ariaHidden }}"><t-image wx:if="{{image}}" t-class="{{prefix}}-image {{classPrefix}}__image" t-class-load="{{prefix}}-class-alt" style="{{imageProps && imageProps.style || ''}}" src="{{image}}" mode="{{imageProps && imageProps.mode || 'aspectFill'}}" lazy="{{imageProps && imageProps.lazy || false}}" loading="{{imageProps && imageProps.loading || 'default'}}" shape="{{imageProps && imageProps.shape || 'round'}}" webp="{{imageProps && imageProps.webp || false}}" error="{{alt || 'default'}}" bind:error="onLoadError"/><template wx:elif="{{iconName || _.isNoEmptyObj(iconData)}}" is="icon" data="{{tClass: classPrefix + '__icon ' + prefix + '-class-icon', name: iconName, ...iconData}}"/><view wx:else class="{{classPrefix}}__text {{prefix}}-class-content"><slot/></view></view></t-badge></view>