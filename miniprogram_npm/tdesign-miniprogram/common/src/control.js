const defaultOption={valueKey:"value",defaultValueKey:"defaultValue",changeEventName:"change",strict:!0};function useControl(e={}){const{valueKey:t,defaultValueKey:a,changeEventName:n,strict:s}=Object.assign(Object.assign({},defaultOption),e),l=this.properties||{},i=l[t],u=l[s?a:t];let o=!1;s&&null!=i&&(o=!0);const c=(e,a,n)=>{this.setData(Object.assign({[`_${t}`]:e},a),n)};return{controlled:o,initValue:o?i:u,set:c,get:()=>this.data[`_${t}`],change:(e,t,a)=>{this.triggerEvent(n,void 0!==t?t:e),o||("function"==typeof a?a():c(e))}}}export{useControl};