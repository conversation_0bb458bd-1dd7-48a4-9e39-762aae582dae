import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-divider`;let Divider=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-content`],this.options={multipleSlots:!0},this.properties=props,this.data={prefix:prefix,classPrefix:name},this.observers={lineColor(){this.setStyle()}},this.methods={setStyle(){const{lineColor:e}=this.properties,o=""+(e?`border-color: ${e};`:"");this.setData({dividerStyle:o})}}}};Divider=__decorate([wxComponent()],Divider);export default Divider;