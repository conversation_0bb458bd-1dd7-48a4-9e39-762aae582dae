import{__awaiter,__decorate}from"tslib";import{SuperComponent,wxComponent}from"../../common/src/index";import config from"../../common/config";import props from"./props";import{getRect,systemInfo}from"../../common/utils";const{prefix:prefix}=config,name=`${prefix}-draggable`;let Draggable=class extends SuperComponent{constructor(){super(...arguments),this.properties=props,this.externalClasses=[`${prefix}-class`],this.data={prefix:prefix,classPrefix:name},this.lifetimes={ready(){this.computedRect()}},this.methods={onTouchStart(t){"none"!==this.properties.direction&&(this.startX=t.touches[0].clientX+systemInfo.windowWidth-this.rect.right,this.startY=t.touches[0].clientY+systemInfo.windowHeight-this.rect.bottom,this.triggerEvent("start",{startX:this.startX,startY:this.startY,rect:this.rect,e:t}))},onTouchMove(t){if("none"===this.properties.direction)return;let e=this.startX-t.touches[0].clientX,i=this.startY-t.touches[0].clientY;"vertical"===this.properties.direction&&(e=systemInfo.windowWidth-this.rect.right),"horizontal"===this.properties.direction&&(i=systemInfo.windowHeight-this.rect.bottom),this.triggerEvent("move",{x:e,y:i,rect:this.rect,e:t})},onTouchEnd(t){return __awaiter(this,void 0,void 0,function*(){"none"!==this.properties.direction&&(yield this.computedRect(),this.triggerEvent("end",{rect:this.rect,e:t}))})},computedRect(){return __awaiter(this,void 0,void 0,function*(){this.rect={right:0,bottom:0,width:0,height:0};try{this.rect=yield getRect(this,`.${this.data.classPrefix}`)}catch(t){}})}}}};Draggable=__decorate([wxComponent()],Draggable);export default Draggable;