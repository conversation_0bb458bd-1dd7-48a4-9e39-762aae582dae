<wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}}" class="{{classPrefix}} class {{prefix}}-class"><block wx:if="{{logo}}"><view class="{{classPrefix}}__logo"><t-image t-class="{{classPrefix}}__icon" src="{{logo.icon}}" wx:if="{{logo.icon}}"/><view class="{{classPrefix}}__title" wx:if="{{logo.title}}">{{logo.title}}</view><t-image t-class="{{classPrefix}}__title-url" src="{{logo.url}}" mode="widthFix" wx:elif="{{logo.url}}"/></view></block><block wx:else><view wx:if="{{links.length > 0}}" class="{{classPrefix}}__link-list"><block wx:for="{{links}}" wx:key="name" wx:for-item="item"><navigator url="{{item.url}}" open-type="{{item.openType}}" hover-class="none" class="{{classPrefix}}__link-item">{{item.name}}</navigator><view aria-hidden="{{true}}" wx:if="{{index !== (links.length - 1)}}" class="{{classPrefix}}__link-line">|</view></block></view><view class="{{classPrefix}}__text">{{text}}</view></block></view>