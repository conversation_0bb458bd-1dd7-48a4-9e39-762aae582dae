<import src="./content.wxml"/><t-overlay wx:if="{{modeType === 'popover'}}" visible="{{visible}}" using-custom-navbar="{{usingCustomNavbar}}" background-color="transparent" z-index="{{zIndex}}"><view class="class {{prefix}}-class {{classPrefix}}"><view class="{{prefix}}-class-reference {{classPrefix}}__reference {{nonOverlay ? classPrefix + '__reference--nonoverlay' : ''}}" style="{{referenceStyle}}"/><view class="{{prefix}}-class-popover {{classPrefix}}__container {{title || body ? classPrefix + '__container--' + modeType : ''}}" style="{{popoverStyle}}"><template is="content" data="{{ prefix, classPrefix, title, body, steps, current, modeType, hideSkip, hideBack, ...buttonProps }}"/></view></view></t-overlay><t-popup wx:elif="{{modeType === 'dialog'}}" visible="{{visible}}" show-overlay="{{!nonOverlay}}" using-custom-navbar="{{usingCustomNavbar}}" z-index="{{zIndex}}" placement="center"><view class="class {{prefix}}-class {{classPrefix}}"><view class="{{prefix}}-class-popover {{classPrefix}}__container {{title || body ? classPrefix + '__container--' + modeType : ''}}"><template is="content" data="{{ prefix, classPrefix, title, body, steps, current, modeType, hideSkip, ...buttonProps }}"/></view></view></t-popup>