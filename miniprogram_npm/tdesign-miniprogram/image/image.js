import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import ImageProps from"./props";import config from"../common/config";import{addUnit,getRect,appBaseInfo}from"../common/utils";import{compareVersion}from"../common/version";const{prefix:prefix}=config,name=`${prefix}-image`;let Image=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-load`,`${prefix}-class-image`,`${prefix}-class-error`],this.options={multipleSlots:!0},this.properties=ImageProps,this.data={prefix:prefix,isLoading:!0,isFailed:!1,innerStyle:"",classPrefix:name},this.preSrc=void 0,this.observers={src(){this.preSrc!==this.properties.src&&this.update()},"width, height"(e,i){this.calcSize(e,i)}},this.methods={onLoaded(e){const i=appBaseInfo.SDKVersion,{mode:t,tId:s}=this.properties,r=compareVersion(i,"2.10.3")<0;if("heightFix"===t&&r){const{height:i,width:t}=e.detail;getRect(this,`#${s||"image"}`).then(e=>{const{height:s}=e,r=(s/i*t).toFixed(2);this.setData({innerStyle:`height: ${addUnit(s)}; width: ${r}px;`})})}this.setData({isLoading:!1,isFailed:!1}),this.triggerEvent("load",e.detail)},onLoadError(e){this.setData({isLoading:!1,isFailed:!0}),this.triggerEvent("error",e.detail)},calcSize(e,i){let t="";e&&(t+=`width: ${addUnit(e)};`),i&&(t+=`height: ${addUnit(i)};`),this.setData({innerStyle:t})},update(){const{src:e}=this.properties;this.preSrc=e,e?this.setData({isLoading:!0,isFailed:!1}):this.onLoadError({errMsg:"图片链接为空"})}}}};Image=__decorate([wxComponent()],Image);export default Image;