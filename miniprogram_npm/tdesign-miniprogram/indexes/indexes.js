import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getRect,throttle,systemInfo}from"../common/utils";import pageScrollMixin from"../mixins/page-scroll";const{prefix:prefix}=config,name=`${prefix}-indexes`;let Indexes=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-sidebar`,`${prefix}-class-sidebar-item`],this.properties=props,this.data={prefix:prefix,classPrefix:name,_height:0,_indexList:[],scrollTop:0,activeAnchor:null,showTips:!1},this.relations={"../indexes-anchor/indexes-anchor":{type:"child"}},this.behaviors=[pageScrollMixin()],this.timer=null,this.groupTop=[],this.sidebar=null,this.currentTouchAnchor=null,this.observers={indexList(t){this.setIndexList(t),this.setHeight(this.data._height)},height(t){this.setHeight(t)}},this.lifetimes={ready(){this.timer=null,this.groupTop=[],this.sidebar=null,0===this.data._height&&this.setHeight(),null===this.data.indexList&&this.setIndexList()}},this.methods={setHeight(t){if(!t){const{windowHeight:e}=systemInfo;t=e}this.setData({_height:t},()=>{this.getAllRect()})},setIndexList(t){if(t)this.setData({_indexList:t});else{const t="A".charCodeAt(0),e=[];for(let i=t,s=t+26;i<s;i+=1)e.push(String.fromCharCode(i));this.setData({_indexList:e})}},getAllRect(){this.getAnchorsRect().then(()=>{this.groupTop.forEach((t,e)=>{const i=this.groupTop[e+1];t.totalHeight=((null==i?void 0:i.top)||1/0)-t.top}),this.setAnchorOnScroll(0)}),this.getSidebarRect()},getAnchorsRect(){return Promise.all(this.$children.map(t=>getRect(t,`.${name}-anchor`).then(e=>{this.groupTop.push({height:e.height,top:e.top,anchor:t.data.index})})))},getSidebarRect(){getRect(this,`#id-${name}__bar`).then(t=>{const{top:e,height:i}=t,{length:s}=this.data._indexList;this.sidebar={top:e,height:i,itemHeight:(i-2*(s-1))/s}})},toggleTips(t){t?this.setData({showTips:!0}):(clearInterval(this.timer),this.timer=setTimeout(()=>{this.setData({showTips:!1})},300))},setAnchorByIndex(t){const{_indexList:e,stickyOffset:i}=this.data,s=e[t];if(null!==this.data.activeAnchor&&this.data.activeAnchor===s)return;const o=this.groupTop.find(t=>t.anchor===s);if(o){this.currentTouchAnchor=s;const t=o.top-i;wx.pageScrollTo({scrollTop:t,duration:0}),this.toggleTips(!0),this.triggerEvent("select",{index:s}),this.setData({activeAnchor:s})}},onClick(t){const{index:e}=t.currentTarget.dataset;this.setAnchorByIndex(e)},onTouchMove(t){this.onAnchorTouch(t)},onTouchCancel(){this.toggleTips(!1)},onTouchEnd(t){this.toggleTips(!1),this.onAnchorTouch(t)},onAnchorTouch:throttle(function(t){const e=(t=>{const e=t-this.sidebar.top;return e<=0?0:e>this.sidebar.height?this.data._indexList.length-1:Math.floor(e/this.sidebar.itemHeight)})(t.changedTouches[0].clientY);this.setAnchorByIndex(e)},1e3/30),setAnchorOnScroll(t){if(!this.groupTop)return;const{sticky:e,stickyOffset:i,activeAnchor:s}=this.data;t+=i;const o=this.groupTop.findIndex(e=>t>=e.top-e.height&&t<=e.top+e.totalHeight-e.height);if(-1===o)return;const h=this.groupTop[o];if(null!==this.currentTouchAnchor?(this.triggerEvent("change",{index:h.anchor}),this.currentTouchAnchor=null):s!==h.anchor&&(this.triggerEvent("change",{index:h.anchor}),this.setData({activeAnchor:h.anchor})),e){const e=h.top-t,s=e<h.height&&e>0&&t>i;this.$children.forEach((n,r)=>{if(r===o){const o=t>i,r=`transform: translate3d(0, ${s?e:0}px, 0); top: ${i}px`;r===n.data.anchorStyle&&o===n.data.sticky||n.setData({sticky:o,active:!0,style:`height: ${h.height}px`,anchorStyle:r})}else if(r+1===o){const t=`transform: translate3d(0, ${s?e-h.height:0}px, 0); top: ${i}px`;t!==n.data.anchorStyle&&n.setData({sticky:!0,active:!0,style:`height: ${h.height}px`,anchorStyle:t})}else n.setData({active:!1,sticky:!1,anchorStyle:""})})}},onScroll({scrollTop:t}){this.setAnchorOnScroll(t)}}}};Indexes=__decorate([wxComponent()],Indexes);export default Indexes;