import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import{getRect,getAnimationFrame,calcIcon}from"../common/utils";import props from"./props";import config from"../common/config";const{prefix:prefix}=config,name=`${prefix}-notice-bar`,THEME_ICON={info:"info-circle-filled",success:"check-circle-filled",warning:"info-circle-filled",error:"error-circle-filled"};let NoticeBar=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-content`,`${prefix}-class-prefix-icon`,`${prefix}-class-operation`,`${prefix}-class-suffix-icon`],this.options={multipleSlots:!0,pureDataPattern:/^__/},this.properties=props,this.data={prefix:prefix,classPrefix:name,loop:-1,__ready:!1},this.observers={marquee(t){"{}"!==JSON.stringify(t)&&"true"!==JSON.stringify(t)||this.setData({marquee:{speed:50,loop:-1,delay:0}})},visible(t){this.data.__ready&&(t?this.show():this.clearNoticeBarAnimation())},prefixIcon(t){this.setPrefixIcon(t)},suffixIcon(t){this.setData({_suffixIcon:calcIcon(t)})},content(){this.data.__ready&&(this.clearNoticeBarAnimation(),this.initAnimation())}},this.lifetimes={created(){this.resetAnimation=wx.createAnimation({duration:0,timingFunction:"linear"})},detached(){this.clearNoticeBarAnimation()},ready(){this.show(),this.setData({__ready:!0})}},this.methods={initAnimation(){const t=`.${name}__content-wrap`,i=`.${name}__content`;getAnimationFrame(this,()=>{Promise.all([getRect(this,i),getRect(this,t)]).then(([t,i])=>{const{marquee:e}=this.properties;if(null!=t&&null!=i&&t.width&&i.width&&!1!==e&&(e||i.width<t.width)){const n=e.speed||50,o=e.delay||0,a=(i.width+t.width)/n*1e3,r=t.width/n*1e3;this.setData({wrapWidth:Number(i.width),nodeWidth:Number(t.width),animationDuration:a,delay:o,loop:e.loop-1,firstAnimationDuration:r}),0!==e.loop&&this.startScrollAnimation(!0)}}).catch(()=>{})})},startScrollAnimation(t=!1){this.clearNoticeBarAnimation();const{wrapWidth:i,nodeWidth:e,firstAnimationDuration:n,animationDuration:o,delay:a}=this.data,r=t?a:0,s=t?n:o;this.setData({animationData:this.resetAnimation.translateX(t?0:i).step().export()}),getAnimationFrame(this,()=>{this.setData({animationData:wx.createAnimation({duration:s,timingFunction:"linear",delay:r}).translateX(-e).step().export()})}),this.nextAnimationContext=setTimeout(()=>{this.data.loop>0?(this.data.loop-=1,this.startScrollAnimation()):0===this.data.loop?this.setData({animationData:this.resetAnimation.translateX(0).step().export()}):this.data.loop<0&&this.startScrollAnimation()},s+r)},show(){this.clearNoticeBarAnimation(),this.setPrefixIcon(this.properties.prefixIcon),this.initAnimation()},clearNoticeBarAnimation(){this.nextAnimationContext&&clearTimeout(this.nextAnimationContext),this.nextAnimationContext=null},setPrefixIcon(t){const{theme:i}=this.properties;this.setData({_prefixIcon:calcIcon(t,THEME_ICON[i])})},onChange(t){const{current:i,source:e}=t.detail;this.triggerEvent("change",{current:i,source:e})},clickPrefixIcon(){this.triggerEvent("click",{trigger:"prefix-icon"})},clickContent(){this.triggerEvent("click",{trigger:"content"})},clickSuffixIcon(){this.triggerEvent("click",{trigger:"suffix-icon"})},clickOperation(){this.triggerEvent("click",{trigger:"operation"})}}}};NoticeBar=__decorate([wxComponent()],NoticeBar);export default NoticeBar;