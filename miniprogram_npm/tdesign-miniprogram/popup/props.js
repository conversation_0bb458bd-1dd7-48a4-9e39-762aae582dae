const props={closeBtn:{type:Boolean},closeOnOverlayClick:{type:Boolean,value:!0},content:{type:String},duration:{type:Number,value:240},externalClasses:{type:Array},overlayProps:{type:Object,value:{}},placement:{type:String,value:"top"},preventScrollThrough:{type:Boolean,value:!0},showOverlay:{type:Boolean,value:!0},usingCustomNavbar:{type:Boolean,value:!1},visible:{type:Boolean,value:null},defaultVisible:{type:Boolean},zIndex:{type:Number,value:11500}};export default props;