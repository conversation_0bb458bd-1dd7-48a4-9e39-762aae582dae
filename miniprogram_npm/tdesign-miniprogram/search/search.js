import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getCharacterLength}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-search`;let Search=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-input-container`,`${prefix}-class-input`,`${prefix}-class-action`,`${prefix}-class-left`,`${prefix}-class-clear`],this.options={multipleSlots:!0},this.properties=props,this.observers={resultList(e){const{isSelected:t}=this.data;e.length?t?this.setData({isShowResultList:!1,isSelected:!1}):this.setData({isShowResultList:!0}):this.setData({isShowResultList:!1})},"clearTrigger, clearable, disabled, readonly"(){this.updateClearIconVisible()}},this.data={classPrefix:name,prefix:prefix,isShowResultList:!1,isSelected:!1,showClearIcon:!0}}updateClearIconVisible(e=!1){const{clearTrigger:t,disabled:s,readonly:i}=this.properties;s||i?this.setData({showClearIcon:!1}):this.setData({showClearIcon:e||"always"===String(t)})}onInput(e){let{value:t}=e.detail;const{maxcharacter:s}=this.properties;if(s&&"number"==typeof s&&s>0){const{characters:e}=getCharacterLength("maxcharacter",t,s);t=e}this.setData({value:t}),this.triggerEvent("change",{value:t})}onFocus(e){const{value:t}=e.detail;this.updateClearIconVisible(!0),this.triggerEvent("focus",{value:t})}onBlur(e){const{value:t}=e.detail;this.updateClearIconVisible(),this.triggerEvent("blur",{value:t})}handleClear(){this.setData({value:""}),this.triggerEvent("clear",{value:""}),this.triggerEvent("change",{value:""})}onConfirm(e){const{value:t}=e.detail;this.triggerEvent("submit",{value:t})}onActionClick(){this.triggerEvent("action-click")}onSelectResultItem(e){const{index:t}=e.currentTarget.dataset,s=this.properties.resultList[t];this.setData({value:s,isSelected:!0}),this.triggerEvent("change",{value:s}),this.triggerEvent("selectresult",{index:t,item:s})}};Search=__decorate([wxComponent()],Search);export default Search;