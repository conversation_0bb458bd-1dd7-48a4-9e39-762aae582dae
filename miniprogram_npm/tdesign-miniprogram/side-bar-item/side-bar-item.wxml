<import src="../common/template/badge"/><import src="../common/template/icon"/><wxs src="../common/utils.wxs" module="_"/><view id="{{tId}}" class="{{_.cls(classPrefix, [['active', active], ['disabled', disabled]])}} class {{prefix}}-class" style="{{_._style([style, customStyle])}}" bind:tap="handleClick" aria-role="button" aria-label="{{ active ? '已选中，' + label : label}}" aria-label="{{ ariaLabel || (badgeProps.dot || badgeProps.count ? (active ? '已选中，' + label + _.getBadgeAriaLabel({ ...badgeProps }) : label + _.getBadgeAriaLabel({ ...badgeProps })) : '') }}" aria-disabled="{{disabled}}"><block wx:if="{{active}}"><view class="{{classPrefix}}__line"></view><view class="{{classPrefix}}__prefix"></view><view class="{{classPrefix}}__suffix"></view></block><template wx:if="{{_icon}}" is="icon" data="{{ tClass: classPrefix + '__icon', ..._icon }}"/><block wx:if="{{badgeProps}}"><template is="badge" data="{{ ...badgeProps, content: label }}"/></block><block wx:else>{{label}}</block></view>