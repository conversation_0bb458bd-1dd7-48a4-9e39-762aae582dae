<wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}}" class="{{classPrefix}} {{classPrefix}}--{{size}} class {{prefix}}-class"><view class="{{classPrefix}}__minus {{classPrefix}}__minus--{{theme}} {{classPrefix}}__icon--{{size}} {{ disabled || disableMinus || currentValue <= min ? classPrefix + '--' + theme + '-disabled': ''}} {{prefix}}-class-minus" catchtap="minusValue" aria-label="{{'减少' + step}}" aria-role="button" aria-disabled="{{disabled || disableMinus || currentValue <= min}}"><t-icon name="remove"/></view><view class="{{classPrefix}}__input--{{theme}} {{ disabled || disableInput ? classPrefix + '--' + theme + '-disabled': ''}}"><input style="{{inputWidth? 'width:' + inputWidth + 'px;': ''}}" class="{{classPrefix}}__input {{classPrefix}}__input--{{size}} {{prefix}}-class-input" disabled="{{ disabled || disableInput }}" type="{{ integer ? 'number' : 'digit' }}" value="{{ currentValue }}" catchinput="handleInput" catchfocus="handleFocus" catchblur="handleBlur"/></view><view class="{{classPrefix}}__plus {{classPrefix}}__plus--{{theme}} {{classPrefix}}__icon--{{size}} {{ disabled || disablePlus|| currentValue >= max ? classPrefix + '--' + theme + '-disabled': ''}} {{prefix}}-class-plus" catchtap="plusValue" aria-label="{{'增加' + step}}" aria-role="button" aria-disabled="{{disabled || disablePlus|| currentValue >= max}}"><t-icon name="add"/></view></view>