import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import transition from"../mixins/transition";import{calcIcon}from"../common/utils";import useCustomNavbar from"../mixins/using-custom-navbar";const{prefix:prefix}=config,name=`${prefix}-toast`;let Toast=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.options={multipleSlots:!0},this.behaviors=[transition(),useCustomNavbar],this.hideTimer=null,this.data={prefix:prefix,classPrefix:name,typeMapIcon:""},this.properties=props,this.lifetimes={detached(){this.destroyed()}},this.pageLifetimes={hide(){this.hide()}},this.methods={show(e){this.hideTimer&&clearTimeout(this.hideTimer);const i={loading:"loading",success:"check-circle",warning:"error-circle",error:"close-circle"}[null==e?void 0:e.theme],t={direction:props.direction.value,duration:props.duration.value,icon:props.icon.value,message:props.message.value,placement:props.placement.value,preventScrollThrough:props.preventScrollThrough.value,theme:props.theme.value},o=Object.assign(Object.assign(Object.assign({},t),e),{visible:!0,isLoading:"loading"===(null==e?void 0:e.theme),_icon:calcIcon(null!=i?i:e.icon)}),{duration:s}=o;this.setData(o),s>0&&(this.hideTimer=setTimeout(()=>{this.hide()},s))},hide(){var e,i;this.data.visible&&(this.setData({visible:!1}),null===(i=null===(e=this.data)||void 0===e?void 0:e.close)||void 0===i||i.call(e),this.triggerEvent("close"))},destroyed(){this.hideTimer&&(clearTimeout(this.hideTimer),this.hideTimer=null),this.triggerEvent("destory")},loop(){}}}};Toast=__decorate([wxComponent()],Toast);export default Toast;