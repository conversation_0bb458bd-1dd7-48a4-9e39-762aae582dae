{"key": "Upload", "label": "上传", "icon": "", "properties": [{"key": "addContent", "type": ["String", "TNode"], "defaultValue": "", "desc": "添加按钮内容。值为空，使用默认图标渲染；值为 slot 则表示使用插槽渲染；其他值无效。", "label": ""}, {"key": "config", "type": ["Object"], "defaultValue": "", "desc": "图片上传配置，视频上传配置，文件上传配置等，包含图片尺寸、图片来源、视频来源、视频拍摄最长时间等。更多细节查看小程序官网。[图片上传](https://developers.weixin.qq.com/miniprogram/dev/api/media/image/wx.chooseImage.html)。[视频上传](https://developers.weixin.qq.com/miniprogram/dev/api/media/video/wx.chooseVideo.html)", "label": ""}, {"key": "deleteBtn", "type": ["String", "TNode"], "defaultValue": "", "desc": "删除图标。值为空，使用默认图标渲染；值为 slot 则表示使用插槽渲染；其他值无效。", "label": ""}, {"key": "fileListDisplay", "type": ["TNode"], "defaultValue": "", "desc": "【开发中】用于完全自定义文件列表内容", "label": ""}, {"key": "files", "type": ["Array"], "defaultValue": "", "desc": "已上传文件列表", "label": ""}, {"key": "gridConfig", "type": ["Object"], "defaultValue": "", "desc": "upload组件每行上传图片列数以及图片的宽度和高度", "label": ""}, {"key": "gutter", "type": ["Number"], "defaultValue": "16", "desc": "预览窗格的 gutter 大小，单位 rpx", "label": ""}, {"key": "imageProps", "type": ["Object"], "defaultValue": "", "desc": "透传 Image 组件全部属性", "label": ""}, {"key": "max", "type": ["Number"], "defaultValue": "0", "desc": "用于控制文件上传数量，值为 0 则不限制", "label": ""}, {"key": "mediaType", "type": ["Array"], "defaultValue": "['image', 'video']", "desc": "支持上传的文件类型，图片或视频", "label": ""}, {"key": "requestMethod", "type": ["Function"], "defaultValue": "", "desc": "自定义上传方法", "label": ""}, {"key": "sizeLimit", "type": ["Number", "Object"], "defaultValue": "", "desc": "图片文件大小限制，单位 KB。可选单位有：`'B' | 'KB' | 'MB' | 'GB'`。示例一：`1000`。示例二：`{ size: 2, unit: 'MB', message: '图片大小不超过 {sizeLimit} MB' }`", "label": ""}], "events": [{"key": "add", "desc": "上传成功后触发，仅包含本次选择的照片；`context.url` 表示选定视频的临时文件路径 (本地路径)。`context.duration` 表示选定视频的时间长度。`context.size`选定视频的数据量大小。更多描述参考 wx.chooseMedia 小程序官网描述", "label": ""}, {"key": "complete", "desc": "上传成功或失败后触发", "label": ""}, {"key": "fail", "desc": "上传失败后触发", "label": ""}, {"key": "remove", "desc": "移除文件时触发", "label": ""}, {"key": "selectChange", "desc": "选择文件或图片之后，上传之前，触发该事件。<br />`params.value` 表示之前已经上传完成的文件列表。<br />`params.currentSelectedFiles` 表示本次上传选中的文件列表", "label": ""}, {"key": "success", "desc": "上传成功后触发，包含所有上传的文件；`context.url` 表示选定视频的临时文件路径 (本地路径)。`context.duration` 表示选定视频的时间长度。`context.size`选定视频的数据量大小。更多描述参考 wx.chooseMedia 小程序官网描述", "label": ""}]}