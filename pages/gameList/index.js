// gameList.js - 重新设计的游戏列表页面
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({
    data: {
        navbarHeight: 88,
        games: [],
        isLoading: false,
        isLoadingMore: false,
        hasError: false,
        errorMessage: '',
        page: 1,
        size: 10,
        hasMore: true,
        total: 0,
        filterType: '', // 游戏类型筛选
        showFilter: false, // 是否显示筛选面板
        isFirstLoad: true // 是否为首次加载
    },

    onLoad() {
        console.log('游戏列表页面加载');

        // 获取导航栏高度
        const app = getApp();
        const systemInfo = wx.getSystemInfoSync();
        let navBarHeight = 88; // 默认高度

        if (systemInfo.statusBarHeight) {
            navBarHeight = systemInfo.statusBarHeight + 44; // 状态栏 + 导航栏
        }

        // 初始化页面数据
        this.setData({
            navbarHeight: navBarHeight,
            isLoading: true,
            hasError: false,
            games: [],
            page: 1,
            hasMore: true
        });

        // 立即开始加载数据
        this.loadGames();

        // 测试API连接
        this.testApiConnection();
    },

    onShow() {
        console.log('游戏列表页面显示');
        // 每次显示页面时都刷新数据，确保列表是最新的
        // 仅在非首次加载时刷新，避免与onLoad重复加载
        if (this.data.isFirstLoad !== false) {
            this.setData({
                isFirstLoad: false
            });
            return; // 首次加载已在onLoad中处理，不需要重复加载
        }

        // 非首次加载时刷新数据
        this.refreshGames();
    },

    onReady() {
        console.log('游戏列表页面渲染完成');
    },

    // 下拉刷新
    onPullDownRefresh() {
        console.log('用户下拉刷新');
        this.refreshGames();

        // 延迟停止下拉刷新动画
        setTimeout(() => {
            wx.stopPullDownRefresh();
        }, 1000);
    },

    // 上拉加载更多
    onReachBottom() {
        console.log('用户上拉加载更多');
        if (this.data.hasMore && !this.data.isLoading && !this.data.isLoadingMore) {
            this.loadGames();
        }
    },

    // 测试API连接
    testApiConnection() {
        console.log('测试API连接...');
        const app = getApp();
        console.log('API基础URL:', app.globalData.apiBaseUrl);

        // 检查网络状态
        wx.getNetworkType({
            success: (res) => {
                console.log('网络类型:', res.networkType);
                if (res.networkType === 'none') {
                    console.log('网络连接不可用，隐藏错误提示');
                }
            },
            fail: (err) => {
                console.error('获取网络状态失败:', err);
            }
        });

        // 检查session状态
        const session = wx.getStorageSync('session');
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                console.log('当前session状态:', {
                    hasToken: !!sessionData.token,
                    expireTime: sessionData.expireTime,
                    isExpired: sessionData.expireTime < Date.now()
                });
            } catch (e) {
                console.error('session数据格式错误:', e);
            }
        } else {
            console.log('没有找到session数据');
        }
    },

    // 刷新游戏列表
    refreshGames() {
        console.log('刷新游戏列表');

        // 重置所有状态，确保完全刷新
        this.setData({
            page: 1,
            games: [],
            hasMore: true,
            hasError: false,
            isLoading: true,
            isLoadingMore: false
        });

        // 显示刷新提示
        wx.showLoading({
            title: '刷新中...',
            mask: true
        });

        // 延迟执行，确保状态完全重置
        setTimeout(() => {
            this.loadGames();
        }, 100);
    },

    // 加载游戏列表
    loadGames() {
        console.log('开始加载游戏列表 - page:', this.data.page);

        if (this.data.isLoading && this.data.page > 1 || this.data.isLoadingMore || !this.data.hasMore) {
            console.log('跳过加载 - 正在加载中或没有更多数据');
            return;
        }

        // 设置加载状态
        if (this.data.page === 1) {
            this.setData({
                isLoading: true
            });

            // 首次加载显示loading
            wx.showLoading({
                title: '加载中...',
                mask: true
            });
        } else {
            this.setData({
                isLoadingMore: true
            });
        }

        console.log('调用API获取未结束游戏列表 - page:', this.data.page, 'size:', this.data.size);

        // 构建API请求参数
        const params = {
            settled: false, // 获取未结束的游戏
            page: this.data.page,
            size: this.data.size
        };

        // 添加可选筛选参数
        if (this.data.filterType) {
            params.type = this.data.filterType;
        }

        // 调用API获取游戏历史记录（未结束的游戏）
        api.games.history(params).then(res => {
            // 隐藏loading
            wx.hideLoading();

            console.log('获取未结束游戏列表成功:', res);

            // 检查返回数据结构
            if (!res || !res.data) {
                console.error('API返回数据为空:', res);
                throw new Error('API返回数据为空');
            }

            // 兼容不同的数据格式
            let gamesList = [];
            let total = 0;

            if (Array.isArray(res.data)) {
                // 如果data直接是数组
                gamesList = res.data;
                total = res.data.length;
            } else if (res.data.list && Array.isArray(res.data.list)) {
                // 如果data是对象，包含list数组
                gamesList = res.data.list;
                total = res.data.total || res.data.list.length;
            } else {
                console.error('返回数据格式不正确:', res.data);
                throw new Error('返回数据格式不正确');
            }

            console.log('解析出的未结束游戏列表:', gamesList, '总数:', total);

            // 格式化游戏数据
            const newGames = gamesList.map(game => {
                // 安全地处理时间格式化
                let formattedTime = '未知时间';
                try {
                    if (game.createTime) {
                        const date = new Date(game.createTime);
                        if (!isNaN(date.getTime())) {
                            formattedTime = `${date.getMonth() + 1}月${date.getDate()}日`;
                        }
                    }
                } catch (e) {
                    console.error('时间格式化失败:', e, game.createTime);
                }

                return {
                    ...game,
                    createTime: formattedTime,
                    // 确保必要字段存在
                    id: game.id || game._id || `game_${Date.now()}_${Math.random()}`,
                    name: game.name || '未命名游戏',
                    type: game.type || '未知类型',
                    playerCount: game.playerCount || (game.players ? game.players.length : 0)
                };
            });

            console.log('格式化后的游戏数据:', newGames);

            // 去重处理，确保不添加重复的游戏
            let updatedGames;
            if (this.data.page === 1) {
                // 首页直接使用新数据
                updatedGames = newGames;
            } else {
                // 加载更多时，需要去除与现有数据重复的部分
                const existingIds = this.data.games.map(game => game.id);
                const uniqueNewGames = newGames.filter(game => !existingIds.includes(game.id));
                updatedGames = [...this.data.games, ...uniqueNewGames];
            }

            const hasMore = this.data.page * this.data.size < total;

            this.setData({
                games: updatedGames,
                total: total,
                hasMore: hasMore,
                page: this.data.page + 1,
                isLoading: false,
                isLoadingMore: false,
                hasError: false
            });

            console.log('更新后的状态 - games数量:', updatedGames.length, 'total:', total, 'hasMore:', hasMore);

            // 如果没有游戏数据，显示提示
            if (updatedGames.length === 0) {
                console.log('没有找到未结束的游戏数据');
            }

        }).catch(err => {
            console.error('获取游戏列表失败:', err);

            // 隐藏loading
            wx.hideLoading();

            // 先检查网络状态，如果是网络问题，则不显示错误提示
            wx.getNetworkType({
                success: (res) => {
                    console.log('错误处理中检查网络状态:', res.networkType);

                    if (res.networkType === 'none') {
                        // 无网络情况下，不显示错误提示
                        console.log('网络连接不可用，隐藏错误提示');
                        this.setData({
                            isLoading: false,
                            isLoadingMore: false,
                            hasError: false // 不显示错误提示
                        });
                        return;
                    }

                    // 有网络但API调用失败，才显示错误信息
                    // 详细的错误信息
                    let errorMessage = '获取游戏列表失败';
                    if (err.code === -1) {
                        errorMessage = '网络连接失败，请检查网络设置';
                    } else if (err.code === 401) {
                        errorMessage = '登录已过期，请重新登录';
                    } else if (err.code === 404) {
                        errorMessage = '未找到游戏数据，请稍后再试';
                    } else if (err.code === 400) {
                        errorMessage = '请求参数错误，请联系开发者';
                    } else if (err.code === 500) {
                        errorMessage = '服务器错误，请稍后再试';
                    } else if (err.message) {
                        errorMessage = err.message;
                    }

                    this.setData({
                        isLoading: false,
                        isLoadingMore: false,
                        hasError: true,
                        errorMessage: errorMessage
                    });

                    // 显示错误提示
                    wx.showToast({
                        title: errorMessage,
                        icon: 'none',
                        duration: 3000
                    });
                },
                fail: () => {
                    // 获取网络状态失败，默认不显示错误
                    this.setData({
                        isLoading: false,
                        isLoadingMore: false,
                        hasError: false
                    });
                }
            });
        });
    },

    // 重新加载
    retryLoad() {
        this.setData({
            hasError: false,
            page: 1,
            games: [],
            hasMore: true
        });
        this.loadGames();
    },

    // 加载测试数据（用于开发调试）
    loadTestData() {
        console.log('加载未结束游戏测试数据');

        const testGames = [{
                id: 'test_game_1',
                name: '测试麻将游戏',
                type: 'mahjong',
                playerCount: 4,
                createTime: '12月15日',
                status: 'playing',
                settled: false
            },
            {
                id: 'test_game_2',
                name: '测试扑克游戏',
                type: 'poker',
                playerCount: 3,
                createTime: '12月14日',
                status: 'playing',
                settled: false
            }
        ];

        this.setData({
            games: testGames,
            total: testGames.length,
            hasMore: false,
            isLoading: false,
            hasError: false
        });

        wx.showToast({
            title: '已加载未结束游戏测试数据',
            icon: 'success'
        });
    },

    // 跳转到创建游戏页面 - 使用路由工具
    navigateToCreateGame() {
        console.log('跳转到创建游戏页面');

        // 使用路由工具进行跳转
        router.navigateTo('createGame');
    },

    // 跳转到游戏页面 - 使用路由工具
    navigateToGame(e) {
        const gameId = e.currentTarget.dataset.id;
        console.log('跳转到游戏页面，ID:', gameId);

        // 直接使用微信原生API进行跳转
        wx.navigateTo({
            url: `/pages/score/index?id=${gameId}`
        }).then(() => {
            console.log('跳转成功');
        }).catch(err => {
            console.error('跳转失败:', err);
            // 如果跳转失败，显示错误提示
            wx.showToast({
                title: '跳转失败: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 2000
            });
        });
    },

    // 显示筛选面板
    showFilterPanel() {
        this.setData({
            showFilter: true
        });
    },

    // 隐藏筛选面板
    hideFilterPanel() {
        this.setData({
            showFilter: false
        });
    },

    // 选择游戏类型
    selectGameType(e) {
        const type = e.currentTarget.dataset.type;
        this.setData({
            filterType: type === this.data.filterType ? '' : type
        });
    },

    // 应用筛选
    applyFilter() {
        this.setData({
            page: 1,
            games: [],
            hasMore: true,
            isLoading: true,
            showFilter: false
        });
        this.loadGames();
    },

    // 重置筛选
    resetFilter() {
        this.setData({
            filterType: '',
            page: 1,
            games: [],
            hasMore: true,
            isLoading: true,
            showFilter: false
        });
        this.loadGames();
    },

    // 阻止事件冒泡
    stopPropagation() {
        // 阻止事件冒泡
    }
});