<!--gameList.wxml - 重新设计的游戏列表页面-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>
  
  <!-- 自定义导航栏 - 参考成功案例 -->
  <custom-navbar
    title="分分记"
    show-back="{{false}}"
    show-home="{{false}}"
  >
    <!-- 添加右侧筛选按钮 -->
    <view slot="right" class="nav-action" bindtap="showFilterPanel">
      <text class="action-icon">⚙️</text>
    </view>
  </custom-navbar>
  
  <!-- 可滚动内容区域 - 参考成功案例的结构 -->
  <scroll-view class="scroll-content" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px); margin-top: {{navbarHeight}}px;">
    <view class="content">
      
      <!-- 创建新游戏卡片 -->
      <view class="create-card">
        <view class="create-btn" bindtap="navigateToCreateGame">
          <view class="create-icon">+</view>
          <view class="create-text">创建新游戏</view>
        </view>
      </view>
      
      <!-- 游戏类型筛选标签 -->
      <view class="filter-tags" wx:if="{{filterType}}">
        <view class="filter-tag">
          <text>{{filterType}}</text>
          <text class="tag-close" bindtap="resetFilter">×</text>
        </view>
      </view>
      
      <!-- 游戏列表区域 -->
      <view class="games-section" wx:if="{{!isLoading}}">
        
        <!-- 有游戏时显示列表 -->
        <view class="games-list" wx:if="{{games.length > 0}}">
          <view class="section-title">
            <view class="mahjong-section-title">进行中的游戏</view>
          </view>
          
          <view class="game-cards">
            <view class="game-card" 
                  wx:for="{{games}}" 
                  wx:key="id" 
                  bindtap="navigateToGame" 
                  data-id="{{item.id}}">
              <view class="game-info">
                <view class="game-name">{{item.name}}</view>
                <view class="game-meta">
                  <text class="game-type">{{item.type}}</text>
                  <text class="game-players">{{item.playerCount}}人</text>
                  <text class="game-time">{{item.createTime}}</text>
                </view>
              </view>
              <view class="game-action">
                <text class="action-text">继续</text>
                <text class="action-icon">›</text>
              </view>
            </view>
          </view>
          
          <!-- 加载更多 -->
          <view class="load-more" wx:if="{{hasMore}}">
            <view class="load-btn" bindtap="loadGames" wx:if="{{!isLoadingMore}}">
              点击加载更多
            </view>
            <view class="loading-text" wx:else>
              加载中...
            </view>
          </view>
          
          <!-- 没有更多数据 -->
          <view class="no-more" wx:if="{{!hasMore && games.length > 0}}">
            <text>没有更多数据了</text>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{games.length === 0}}">
          <view class="empty-icon">🀄</view>
          <view class="empty-title">暂无进行中的游戏</view>
          <view class="empty-desc">点击上方按钮创建新游戏</view>
        </view>
        
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" wx:if="{{isLoading}}">
        <view class="loading-text">加载中...</view>
      </view>
      
    </view>
  </scroll-view>
  
  <!-- 筛选面板 -->
  <view class="filter-overlay" wx:if="{{showFilter}}" bindtap="hideFilterPanel">
    <view class="filter-panel" catchtap="stopPropagation">
      <view class="filter-header">
        <text class="filter-title">筛选游戏</text>
        <text class="filter-close" bindtap="hideFilterPanel">×</text>
      </view>
      
      <view class="filter-section">
        <view class="filter-section-title">游戏类型</view>
        <view class="filter-options">
          <view class="filter-option {{filterType === 'mahjong' ? 'active' : ''}}" 
                bindtap="selectGameType" data-type="mahjong">麻将</view>
          <view class="filter-option {{filterType === 'poker' ? 'active' : ''}}" 
                bindtap="selectGameType" data-type="poker">扑克</view>
          <view class="filter-option {{filterType === 'board' ? 'active' : ''}}" 
                bindtap="selectGameType" data-type="board">棋类</view>
          <view class="filter-option {{filterType === 'other' ? 'active' : ''}}" 
                bindtap="selectGameType" data-type="other">其他</view>
        </view>
      </view>
      
      <view class="filter-actions">
        <button class="filter-reset" bindtap="resetFilter">重置</button>
        <button class="filter-apply" bindtap="applyFilter">应用筛选</button>
      </view>
    </view>
  </view>
  
</view>
