/**gameList.wxss - 重新设计的游戏列表页面样式**/

/* 覆盖全局样式，确保页面可以滚动 - 完全参考成功案例 */
page {
  height: auto !important;
  background-color: #f8f4e9 !important; /* 与首页匹配的米色背景 */
}

/* 容器样式 - 完全参考成功案例 */
.container {
  min-height: 100vh;
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  /* 不使用flex布局，让内容自然流动 */
}

/* 背景麻将装饰元素 - 与设计标准保持一致 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

/* 导航栏操作按钮 */
.nav-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.action-icon {
  font-size: 22px;
}

/* 滚动内容区域 - 参考成功案例 */
.scroll-content {
  background-color: transparent;
  box-sizing: border-box;
  padding-bottom: 110rpx; /* 为底部导航栏预留空间 */
}

.content {
  padding: 32rpx;
  padding-bottom: calc(32rpx + 110rpx + env(safe-area-inset-bottom)); /* 底部添加安全区域 */
  margin-top: 0 !important; /* 覆盖全局样式的 margin-top: 88px */
  flex: none !important; /* 覆盖全局样式的 flex: 1 */
}

/* 筛选标签 */
.filter-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
}

.filter-tag {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 32rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(212, 175, 55, 0.3);
}

.tag-close {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 筛选面板 */
.filter-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.filter-panel {
  width: 80%;
  max-height: 80%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 16rpx 48rpx rgba(0,0,0,0.15);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
}

.filter-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #8B0000;
}

.filter-close {
  font-size: 40rpx;
  color: #8B0000;
}

.filter-section {
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.filter-option {
  width: calc(25% - 16rpx);
  margin: 8rpx;
  height: 72rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

.filter-option.active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

.filter-actions {
  display: flex;
  padding: 24rpx;
}

.filter-reset, .filter-apply {
  flex: 1;
  margin: 0 8rpx;
  height: 88rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16rpx;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-reset {
  background-color: #f5f5f5;
  color: #666;
}

.filter-apply {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3);
}

/* 创建游戏卡片 */
/* 创建游戏卡片 - 应用统一设计标准 */
.create-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx; /* 统一内边距 */
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 8rpx solid #d4af37; /* 统一金色边框 */
}

.create-card:active {
  transform: scale(0.98); /* 统一点击效果 */
  background-color: rgba(248, 244, 233, 0.3);
}

.create-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 统一金色渐变 */
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
}

.create-btn:active {
  background-color: rgba(230, 196, 108, 0.25);
  transform: scale(0.98);
}

.create-icon {
  font-size: 48rpx;
  font-weight: 300;
  color: #8B0000;
  margin-right: 16rpx;
  line-height: 1;
}

.create-text {
  font-size: 32rpx;
  color: #8B0000;
  font-weight: 500;
}

/* 游戏列表区域 */
.games-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
  color: #333;
}

/* 统一标题样式 - 与设计标准保持一致 */
.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 12rpx 20rpx; /* 与设计标准保持一致 */
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx; /* 统一圆角 */
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
  font-size: 28rpx; /* 统一字体大小 */
  margin-bottom: 16rpx;
}

.game-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* 统一间距 */
}

/* 游戏卡片 - 应用统一设计标准 */
.game-card {
  background-color: rgba(248, 244, 233, 0.4); /* 统一次要卡片背景 */
  border-radius: 12rpx;
  padding: 24rpx; /* 统一内边距 */
  border: 1rpx solid rgba(212, 175, 55, 0.15); /* 统一边框 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0; /* 移除底部边距，使用gap */
}

.game-card:last-child {
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.15); /* 保持一致的边框 */
}

.game-card:active {
  background-color: rgba(230, 196, 108, 0.25); /* 统一点击效果 */
  border-color: rgba(212, 175, 55, 0.3);
  transform: scale(0.98);
}

.game-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 游戏信息样式 - 统一字体规范 */
.game-name {
  font-size: 32rpx; /* 统一标题字体 */
  font-weight: 500;
  color: #333; /* 统一主要文字颜色 */
  margin-bottom: 8rpx;
}

.game-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx; /* 统一说明文字字体 */
  color: #666; /* 统一次要文字颜色 */
}

.game-type {
  background-color: #f8f4e9;
  color: #8B0000; /* 统一深红色 */
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  font-size: 22rpx; /* 统一小字体 */
  border: 1rpx solid #d4af37; /* 统一金色边框 */
}

.game-players {
  margin-right: 16rpx;
}

.game-time {
  color: #999; /* 统一浅色文字 */
}

.game-action {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
}

.action-text {
  font-size: 26rpx; /* 统一正文字体 */
  color: #8B0000; /* 统一深红色 */
  margin-right: 8rpx;
}

.action-icon {
  font-size: 32rpx; /* 调整图标大小 */
  color: #8B0000; /* 统一深红色 */
  font-weight: 500;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 32rpx 0;
  margin-top: 16rpx;
}

.load-btn {
  color: #8B0000;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: 1rpx solid #d4af37;
  border-radius: 16rpx;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(212, 175, 55, 0.2));
}

.load-btn:active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.no-more text {
  color: #999;
  font-size: 26rpx;
}

/* 空状态 - 应用统一设计标准 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx; /* 统一内边距 */
  background-color: #ffffff;
  border-radius: 16rpx; /* 统一圆角 */
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04); /* 统一阴影 */
  text-align: center;
  border-left: 8rpx solid #d4af37; /* 统一金色边框 */
}

.empty-icon {
  font-size: 100rpx; /* 调整图标大小 */
  margin-bottom: 24rpx;
  opacity: 0.5; /* 降低透明度 */
}

.empty-title {
  font-size: 28rpx; /* 统一副标题字体 */
  color: #8B0000; /* 统一深红色 */
  margin-bottom: 12rpx;
  font-weight: 600;
}

.empty-desc {
  font-size: 24rpx; /* 统一说明文字字体 */
  color: #666; /* 统一次要文字颜色 */
  text-align: center;
  line-height: 1.5;
}

/* 加载状态 - 统一样式 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-state .loading-text {
  font-size: 28rpx; /* 统一副标题字体 */
  color: #666; /* 统一次要文字颜色 */
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.error-title {
  font-size: 32rpx;
  color: #8B0000;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.error-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.error-actions {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.retry-btn, .test-btn {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  border-radius: 16rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
  flex: 1;
  max-width: 200rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}

.test-btn {
  background: linear-gradient(135deg, #8fb35a, #52c41a);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

/* 响应式设计 - 与设计标准保持一致 */
@media screen and (max-width: 600rpx) {
  .content {
    padding: 20rpx;
    padding-top: 12rpx;
  }

  .create-card,
  .games-section,
  .empty-state {
    padding: 28rpx;
    margin-bottom: 24rpx;
  }

  .game-card {
    padding: 20rpx;
  }

  .game-name {
    font-size: 28rpx;
  }

  .game-meta {
    font-size: 22rpx;
  }

  .action-text {
    font-size: 24rpx;
  }

  .action-icon {
    font-size: 28rpx;
  }

  .create-icon {
    font-size: 40rpx;
  }

  .create-text {
    font-size: 28rpx;
  }

  .empty-icon {
    font-size: 80rpx;
  }

  .empty-title {
    font-size: 24rpx;
  }

  .empty-desc {
    font-size: 22rpx;
  }

  .error-title {
    font-size: 28rpx;
  }

  .error-desc {
    font-size: 22rpx;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1000rpx) {
  .content {
    max-width: 800rpx;
    margin: 0 auto;
    padding: 40rpx;
  }

  .create-card,
  .games-section {
    padding: 40rpx;
  }

  .game-cards {
    gap: 20rpx;
  }
}
