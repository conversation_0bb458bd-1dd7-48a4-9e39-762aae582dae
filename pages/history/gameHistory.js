// pages/history/gameHistory.js
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({

    /**
     * 页面的初始数据
     */
    data: {
        statusBarHeight: 20,
        navbarHeight: 88, // 自定义导航栏高度
        game: null,
        gameId: '',
        showOptionsMenu: false,
        showRoundDetail: false,
        selectedRound: 0,
        initialScore: 0, // 初始分数
        isLoading: false // 加载状态
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        if (options.id) {
            this.setData({
                gameId: options.id
            });
            this.loadGame();
        } else {
            wx.showToast({
                title: '游戏ID不存在',
                icon: 'none'
            });
            setTimeout(() => {
                wx.navigateBack();
            }, 1500);
        }
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        // 每次显示页面时刷新游戏数据
        if (this.data.gameId) {
            this.loadGame();
        }
    },

    /**
     * 加载游戏数据
     */
    loadGame() {
        const { gameId } = this.data;

        if (this.data.isLoading) return;

        this.setData({ isLoading: true });

        // 显示加载中提示
        wx.showLoading({
            title: '加载中...',
        });

        // 调用API获取游戏详情
        api.games.detail(gameId).then(res => {
            wx.hideLoading();
            console.log('获取游戏详情成功:', res);

            const game = res.data;

            // 检查游戏数据
            if (!game) {
                wx.showToast({
                    title: '游戏数据不存在',
                    icon: 'none',
                    duration: 2000
                });
                setTimeout(() => {
                    wx.navigateBack();
                }, 2000);
                return;
            }

            // 获取初始分数
            const initialScore = game.initialScore || 0;

            // 格式化轮次时间和处理分数数据
            if (game.rounds && game.rounds.length > 0) {
                game.rounds.forEach(round => {
                    // 格式化时间
                    const date = new Date(round.time);
                    round.timeFormatted = `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()}`;

                    // 处理分数数据 - 将scores对象数组转换为玩家索引对应的分数数组
                    if (round.scores && Array.isArray(round.scores)) {
                        // 创建一个与玩家数量相同的分数数组，初始值为0
                        const scoresArray = new Array(game.players.length).fill(0);

                        // 遍历API返回的scores对象数组
                        round.scores.forEach(scoreObj => {
                            // 根据playerId找到对应的玩家索引
                            const playerIndex = game.players.findIndex(player => player.id === scoreObj.playerId);
                            if (playerIndex !== -1) {
                                // 将分数填入对应玩家的索引位置
                                scoresArray[playerIndex] = scoreObj.score;
                            }
                        });

                        // 用处理后的数组替换原始scores
                        round.processedScores = scoresArray;
                    } else {
                        // 如果没有scores数据或格式不正确，创建一个全0的数组
                        round.processedScores = new Array(game.players.length).fill(0);
                    }
                });
            }

            this.setData({
                game,
                initialScore,
                isLoading: false
            });
        }).catch(err => {
            wx.hideLoading();
            console.error('获取游戏详情失败:', err);

            this.setData({ isLoading: false });

            wx.showToast({
                title: '获取游戏详情失败: ' + (err.message || '未知错误'),
                icon: 'none',
                duration: 2000
            });

            // 如果是404错误，返回上一页
            if (err.code === 404) {
                setTimeout(() => {
                    wx.navigateBack();
                }, 1500);
            }
        });
    },

    // 导航栏高度回调
    onNavbarHeight(e) {
        this.setData({
            navbarHeight: e.detail.height
        });
    },

    /**
     * 返回上一页
     */
    navigateBack() {
        router.navigateBack();
    },

    /**
     * 显示选项菜单
     */
    showOptions() {
        this.setData({
            showOptionsMenu: true
        });
    },

    /**
     * 隐藏选项菜单
     */
    hideOptions() {
        this.setData({
            showOptionsMenu: false
        });
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
        // 阻止事件冒泡
    },

    /**
     * 跳转到结算页面
     */
    navigateToSettlement() {
        const { gameId } = this.data;
        this.hideOptions();

        router.navigateTo('settlement', { id: gameId });
    },

    /**
     * 删除游戏
     */
    deleteGame() {
        const { gameId } = this.data;
        this.hideOptions();

        if (this.data.isLoading) return;

        wx.showModal({
            title: '删除游戏',
            content: '确定要删除此游戏及所有记录吗？此操作不可恢复。',
            confirmColor: '#f5222d',
            success: (res) => {
                if (res.confirm) {
                    this.setData({ isLoading: true });

                    // 显示加载中提示
                    wx.showLoading({
                        title: '删除中...',
                        mask: true
                    });

                    // 调用API删除游戏
                    api.games.delete(gameId).then(res => {
                        wx.hideLoading();
                        console.log('删除游戏成功:', res);

                        // 显示成功提示
                        wx.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });

                        this.setData({ isLoading: false });

                        // 返回上一页
                        setTimeout(() => {
                            wx.navigateBack();
                        }, 1500);
                    }).catch(err => {
                        wx.hideLoading();
                        console.error('删除游戏失败:', err);

                        wx.showToast({
                            title: '删除游戏失败: ' + (err.message || '未知错误'),
                            icon: 'none',
                            duration: 2000
                        });

                        this.setData({ isLoading: false });
                    });
                }
            }
        });
    },

    /**
     * 查看轮次详情
     */
    viewRoundDetail(e) {
        const index = e.currentTarget.dataset.index;

        // 检查索引是否有效
        const { game } = this.data;
        if (!game || !game.rounds || index < 0 || index >= game.rounds.length) {
            wx.showToast({
                title: '无效的轮次',
                icon: 'none'
            });
            return;
        }

        this.setData({
            showRoundDetail: true,
            selectedRound: index
        });
    },

    /**
     * 隐藏轮次详情
     */
    hideRoundDetail() {
        this.setData({
            showRoundDetail: false
        });
    },

    /**
     * 删除轮次
     */
    deleteRound(e) {
        const index = e.currentTarget.dataset.index;
        const { game } = this.data;

        if (!game || !game.rounds || index < 0 || index >= game.rounds.length) {
            wx.showToast({
                title: '无效的轮次',
                icon: 'none'
            });
            this.hideRoundDetail();
            return;
        }

        const round = game.rounds[index];

        if (this.data.isLoading) return;

        // 显示确认对话框
        wx.showModal({
            title: '删除轮次',
            content: '确定要删除这个轮次吗？此操作不可恢复。',
            confirmColor: '#f5222d',
            success: (res) => {
                if (res.confirm) {
                    this.setData({ isLoading: true });

                    // 显示加载中提示
                    wx.showLoading({
                        title: '删除中...',
                        mask: true
                    });

                    // 调用API删除轮次
                    api.games.deleteRound(game.id, round.id).then(res => {
                        wx.hideLoading();
                        console.log('删除轮次成功:', res);

                        // 隐藏轮次详情弹窗
                        this.setData({
                            showRoundDetail: false,
                            isLoading: false
                        });

                        // 显示成功提示
                        wx.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });

                        // 重新加载游戏数据
                        setTimeout(() => {
                            this.loadGame();
                        }, 500);
                    }).catch(err => {
                        wx.hideLoading();
                        console.error('删除轮次失败:', err);

                        wx.showToast({
                            title: '删除失败: ' + (err.message || '未知错误'),
                            icon: 'none',
                            duration: 2000
                        });

                        this.setData({ isLoading: false });
                    });
                }
            }
        });
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        this.loadGame();
        wx.stopPullDownRefresh();
    }
})