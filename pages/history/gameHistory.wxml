<!--pages/history/gameHistory.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>
  
  <!-- 自定义导航栏 -->
  <custom-navbar
    title="{{game.name}}"
    show-back="{{true}}"
    show-home="{{true}}"
    bind:height="onNavbarHeight"
  >
    <!-- 右侧操作按钮 -->
    <view slot="right" class="nav-action" bindtap="showOptions">
      <text class="action-icon">⋮</text>
    </view>
  </custom-navbar>
  
  <!-- 内容区域 -->
  <scroll-view class="scroll-content" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px); margin-top: {{navbarHeight}}px;">
    <view class="content">
      <!-- 游戏战绩卡片 -->
      <view class="game-summary-card">
        <view class="section-title">
          <view class="mahjong-section-title">战绩总览</view>
        </view>

        <!-- 玩家分数列表 -->
        <view class="player-list">
          <view class="player-item" wx:for="{{game.players}}" wx:key="id">
            <view class="player-avatar" style="background-color: {{item.color}}">
              {{item.name[0]}}
            </view>
            <view class="player-info">
              <text class="player-name">{{item.name}}</text>
              <view class="player-score {{item.score > initialScore ? 'positive' : (item.score < initialScore ? 'negative' : '')}}">
                {{item.score}}<text class="score-unit">分</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 历史记录列表 -->
      <view class="history-section" wx:if="{{game.rounds && game.rounds.length > 0}}">
        <view class="section-title">
          <view class="mahjong-section-title">对局记录 ({{game.rounds.length}}局)</view>
        </view>

        <view class="history-list">
          <view class="history-card" wx:for="{{game.rounds}}" wx:key="index" bindtap="viewRoundDetail" data-index="{{index}}">
            <view class="round-header">
              <view class="round-title">第 {{index + 1}} 局</view>
              <view class="round-time">{{item.timeFormatted}}</view>
            </view>

            <!-- 该局玩家得分 - 只显示有变化的分数 -->
            <view class="round-scores">
              <view class="round-score-item" wx:for="{{item.processedScores}}" wx:for-item="score" wx:for-index="playerIndex" wx:key="playerIndex" wx:if="{{score !== 0}}">
                <view class="score-player-info">
                  <view class="score-player-avatar" style="background-color: {{game.players[playerIndex].color}}">
                    {{game.players[playerIndex].name[0]}}
                  </view>
                  <text class="score-player-name">{{game.players[playerIndex].name}}</text>
                </view>
                <view class="score-value {{score > 0 ? 'positive' : 'negative'}}">
                  {{score > 0 ? '+' : ''}}{{score}}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 没有历史记录时的提示 -->
      <view class="empty-state" wx:if="{{!game.rounds || game.rounds.length === 0}}">
        <view class="empty-icon">🀄</view>
        <view class="empty-title">暂无对局记录</view>
        <view class="empty-desc">返回记分页面开始记分</view>
      </view>
    </view>
  </scroll-view>
  
  <!-- 选项菜单弹窗 -->
  <view class="options-overlay" wx:if="{{showOptionsMenu}}" bindtap="hideOptions">
    <view class="options-menu" catchtap="stopPropagation">
      <view class="options-header">
        <text class="options-title">游戏选项</text>
        <text class="options-close" bindtap="hideOptions">×</text>
      </view>
      <view class="options-list">
        <view class="option-item" bindtap="navigateToSettlement">
          <text class="option-icon">🏁</text>
          <text class="option-text">结算游戏</text>
        </view>
        <view class="option-item" bindtap="deleteGame">
          <text class="option-icon">🗑️</text>
          <text class="option-text delete-text">删除游戏</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 轮次详情弹窗 -->
  <view class="round-detail-overlay" wx:if="{{showRoundDetail}}" bindtap="hideRoundDetail">
    <view class="round-detail-card" catchtap="stopPropagation">
      <view class="detail-header">
        <text class="detail-title">第 {{selectedRound + 1}} 局详情</text>
        <text class="detail-close" bindtap="hideRoundDetail">×</text>
      </view>
      
      <view class="detail-content">
        <view class="detail-time">{{game.rounds[selectedRound].timeFormatted}}</view>
        
        <view class="detail-scores">
          <view class="detail-score-item" wx:for="{{game.players}}" wx:key="id" wx:for-index="playerIndex">
            <view class="detail-player">
              <view class="detail-avatar" style="background-color: {{item.color}}">
                {{item.name[0]}}
              </view>
              <text class="detail-name">{{item.name}}</text>
            </view>
            <view class="detail-score {{game.rounds[selectedRound].processedScores[playerIndex] > 0 ? 'positive' : (game.rounds[selectedRound].processedScores[playerIndex] < 0 ? 'negative' : '')}}">
              {{game.rounds[selectedRound].processedScores[playerIndex] > 0 ? '+' : ''}}{{game.rounds[selectedRound].processedScores[playerIndex]}}
            </view>
          </view>
        </view>
      </view>
      
      <view class="detail-actions">
        <button class="btn btn-outline" bindtap="hideRoundDetail">关闭</button>
        <button class="btn btn-primary" bindtap="deleteRound" data-index="{{selectedRound}}">删除此局</button>
      </view>
    </view>
  </view>
</view> 