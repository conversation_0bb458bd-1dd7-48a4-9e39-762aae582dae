/* pages/history/gameHistory.wxss */

/* 页面根元素样式 - 强制覆盖全局样式 */
page {
  background-color: #f8f4e9 !important; /* 与首页匹配的米色背景 */
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

/* 背景麻将装饰元素 - 简化装饰 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏右侧操作按钮 */
.nav-action {
  width: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 32rpx;
  font-weight: 600;
  color: #8B0000;
}

/* 滚动内容区域 */
.scroll-content {
  width: 100%;
  box-sizing: border-box;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1002;
}

.content {
  padding: 24rpx;
  padding-top: 16rpx;
  padding-bottom: 120rpx;
  margin-top: 0 !important;
  flex: none !important;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1003;
  min-height: calc(100vh - 200rpx);
}

/* 简化标题样式 */
.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx;
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25);
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.section-title {
  margin-bottom: 20rpx;
}

/* 游戏战绩卡片 - 与其他页面保持一致 */
.game-summary-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37;
}

/* 玩家列表 - 优化间距 */
.player-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 20rpx 16rpx;
  background-color: rgba(248, 244, 233, 0.4);
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 80rpx;
}

.player-item:hover {
  background-color: rgba(248, 244, 233, 0.6);
  border-color: rgba(212, 175, 55, 0.25);
}

.player-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: #ffffff;
  margin-right: 16rpx;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
}

.player-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.player-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.player-score {
  font-size: 30rpx;
  font-weight: 700;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  min-width: 100rpx;
}

.player-score.positive {
  color: #52c41a;
}

.player-score.negative {
  color: #f5222d;
}

/* 历史记录部分 - 与其他页面保持一致 */
.history-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-card {
  padding: 20rpx;
  background-color: rgba(248, 244, 233, 0.4);
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.history-card:active {
  background-color: rgba(230, 196, 108, 0.25);
  transform: scale(0.98);
  border-color: rgba(212, 175, 55, 0.3);
}

.round-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.15);
}

.round-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #8B0000;
}

.round-time {
  font-size: 22rpx;
  color: #999;
}

.round-scores {
  display: flex;
  flex-direction: column;
}

/* 简化分割线样式 */
.round-score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14rpx 0;
  min-height: 56rpx;
  position: relative;
}

.round-score-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  background-color: rgba(212, 175, 55, 0.1);
}

.score-player-info {
  display: flex;
  align-items: center;
  height: 100%;
}

.score-player-avatar {
  width: 44rpx;
  height: 44rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22rpx;
  color: #ffffff;
  margin-right: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.08);
  flex-shrink: 0;
}

.score-player-name {
  font-size: 24rpx;
  color: #333;
}

.score-value {
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  min-width: 80rpx;
}

.score-value.positive {
  color: #52c41a;
}

.score-value.negative {
  color: #f5222d;
}

.score-unit {
  font-size: 22rpx;
  font-weight: 400;
  margin-left: 2rpx;
}

/* 空状态样式 - 与其他页面保持一致 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  text-align: center;
  border-left: 8rpx solid #d4af37;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B0000;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #666;
}

/* 选项菜单弹窗 - 现代化设计 */
.options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 2000;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.options-menu {
  width: 70%;
  background-color: #ffffff;
  border-radius: 20rpx 0 0 20rpx;
  overflow: hidden;
  box-shadow: -4rpx 0 32rpx rgba(0, 0, 0, 0.15);
  margin-top: 120rpx;
  max-height: 70%;
  display: flex;
  flex-direction: column;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
}

.options-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #8B0000;
}

.options-close {
  font-size: 36rpx;
  color: #8B0000;
  padding: 0 8rpx;
}

.options-list {
  padding: 12rpx 0;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx 28rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item:active {
  background-color: rgba(248, 244, 233, 0.5);
}

.option-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.option-text {
  font-size: 26rpx;
  color: #333;
}

.delete-text {
  color: #f5222d;
}

/* 轮次详情弹窗 - 现代化设计 */
.round-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.round-detail-card {
  width: 88%;
  max-width: 560rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12), 0 2rpx 8rpx rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 32rpx 20rpx 32rpx;
  background: linear-gradient(135deg, #e6c46c, #f8f4e9);
}

.detail-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B0000;
}

.detail-close {
  font-size: 36rpx;
  color: #8B0000;
  padding: 0 8rpx;
  cursor: pointer;
}

.detail-content {
  padding: 28rpx 32rpx;
}

.detail-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.detail-scores {
  margin-bottom: 20rpx;
}

.detail-score-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  min-height: 56rpx;
}

.detail-score-item:last-child {
  border-bottom: none;
}

.detail-player {
  display: flex;
  align-items: center;
  height: 100%;
}

.detail-avatar {
  width: 52rpx;
  height: 52rpx;
  border-radius: 14rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #fff;
  margin-right: 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.15);
  flex-shrink: 0;
}

.detail-name {
  font-size: 26rpx;
  color: #8B0000;
  font-weight: 500;
}

.detail-score {
  font-size: 32rpx;
  font-weight: 700;
  color: #8B0000;
  min-width: 80rpx;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}

.detail-score.positive {
  color: #52c41a;
}

.detail-score.negative {
  color: #f5222d;
}

.detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  margin-top: 20rpx;
  padding: 0 32rpx 28rpx;
}

.btn {
  border-radius: 14rpx;
  font-size: 26rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border: none;
  padding: 0 28rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.btn-primary {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

.btn-outline {
  background-color: #ffffff;
  color: #8B0000;
  border: 1rpx solid #d4af37;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 600rpx) {
  .content {
    padding: 20rpx;
    padding-top: 12rpx;
  }

  .game-summary-card,
  .history-section {
    padding: 28rpx;
    margin-bottom: 24rpx;
  }

  .player-item {
    padding: 16rpx 12rpx;
    min-height: 72rpx;
  }

  .player-avatar {
    width: 64rpx;
    height: 64rpx;
    font-size: 26rpx;
    margin-right: 12rpx;
  }

  .player-name {
    font-size: 26rpx;
  }

  .player-score {
    font-size: 28rpx;
    min-width: 90rpx;
  }

  .history-card {
    padding: 16rpx;
  }

  .round-title {
    font-size: 24rpx;
  }

  .round-time {
    font-size: 20rpx;
  }

  .score-player-avatar {
    width: 40rpx;
    height: 40rpx;
    font-size: 20rpx;
    margin-right: 10rpx;
  }

  .score-player-name {
    font-size: 22rpx;
  }

  .score-value {
    font-size: 26rpx;
    min-width: 70rpx;
  }

  .options-menu {
    width: 80%;
  }

  .round-detail-card {
    width: 92%;
    max-width: 500rpx;
  }

  .detail-content {
    padding: 24rpx 28rpx;
  }

  .detail-avatar {
    width: 48rpx;
    height: 48rpx;
    font-size: 24rpx;
  }

  .detail-name {
    font-size: 24rpx;
  }

  .detail-score {
    font-size: 28rpx;
  }

  .btn {
    height: 72rpx;
    line-height: 72rpx;
    font-size: 24rpx;
    padding: 0 24rpx;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1000rpx) {
  .content {
    max-width: 800rpx;
    margin: 0 auto;
    padding: 32rpx;
  }

  .game-summary-card,
  .history-section {
    padding: 40rpx;
  }

  .player-list {
    gap: 16rpx;
  }

  .history-list {
    gap: 20rpx;
  }
}