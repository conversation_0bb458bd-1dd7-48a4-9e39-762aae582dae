<!--pages/history/index.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>

  <!-- 自定义导航栏 - 简化设计，只保留标题和筛选按钮 -->
  <custom-navbar
    title="历史记录"
    show-back="{{false}}"
    show-home="{{false}}"
    show-capsule="{{false}}"
    show-right-slot="{{true}}"
  >
    <!-- 右侧筛选按钮 -->
    <view slot="right" class="nav-action {{filterType || startDate || endDate ? 'active' : ''}}" bindtap="showFilterPanel">
      <text class="filter-icon">🔍</text>
      <text class="action-text">筛选</text>
      <view class="filter-badge" wx:if="{{filterType || startDate || endDate}}"></view>
    </view>
  </custom-navbar>
  
  <!-- 筛选条件指示器 -->
  <view class="filter-indicator" wx:if="{{filterType || startDate || endDate}}">
    <view class="filter-tags">
      <view class="filter-tag" wx:if="{{filterType}}">
        {{filterType === 'mahjong' ? '麻将' : filterType === 'poker' ? '扑克' : filterType}}
        <text class="filter-tag-close" catchtap="selectGameType" data-type="{{filterType}}">×</text>
      </view>
      <view class="filter-tag" wx:if="{{startDate}}">
        开始: {{startDate}}
        <text class="filter-tag-close" catchtap="clearStartDate">×</text>
      </view>
      <view class="filter-tag" wx:if="{{endDate}}">
        结束: {{endDate}}
        <text class="filter-tag-close" catchtap="clearEndDate">×</text>
      </view>
    </view>
    <view class="filter-reset" bindtap="resetFilter">重置</view>
  </view>
  
  <!-- 内容区域 - 适配新的导航栏和筛选指示器 -->
  <scroll-view class="scroll-content" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px{{filterType || startDate || endDate ? ' - 44px' : ''}}); margin-top: {{navbarHeight}}px;" bindscrolltolower="onReachBottom" enhanced="{{true}}" show-scrollbar="{{false}}" bounces="{{false}}" fast-deceleration="{{false}}">
    <view class="content">
      <!-- 游戏历史标题 - 只有当有游戏时才显示 -->
    <view class="section-title" wx:if="{{games.length > 0}}">
      <view class="mahjong-section-title">游戏历史</view>
    </view>
    
    <!-- 历史游戏列表 -->
    <view class="game-list" wx:if="{{games.length > 0}}">
      <view class="game-card" wx:for="{{games}}" wx:key="id" bindtap="navigateToGame" data-id="{{item.id}}">
        <view class="game-header">
          <view class="game-name">{{item.name}}</view>
          <view class="game-time">{{item.timeFormatted}}</view>
        </view>
        
        <!-- 玩家得分列表（纵向排列） -->
        <view class="game-players-vertical">
          <view class="player-row" wx:for="{{item.players}}" wx:for-item="player" wx:key="id">
            <view class="player-avatar" style="background-color: {{player.color}}">
              {{player.name[0]}}
            </view>
            <view class="player-name">{{player.name}}</view>
            <view class="player-score {{player.score > (item.initialScore || 0) ? 'positive' : (player.score < (item.initialScore || 0) ? 'negative' : '')}}">
              {{player.score}}
            </view>
          </view>
        </view>
        
        <view class="game-footer">
          <view class="game-rounds">共 {{item.playerCount}} 人</view>
          <view class="game-action">
            <text class="action-text">查看详情</text>
            <text class="action-icon">›</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{games.length > 0 && hasMore}}">
      <view class="load-btn" wx:if="{{!isLoading}}" bindtap="loadGames">点击加载更多</view>
      <view class="loading-text" wx:else>加载中...</view>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{games.length > 0 && !hasMore}}">
      <text>没有更多数据了</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{games.length === 0 && !isLoading}}">
      <view class="empty-icon">🀄</view>
      <view class="empty-text">暂无历史记录</view>
      <view class="empty-subtext">开始游戏记分后会在这里显示</view>
    </view>
    
      <!-- 加载中状态 -->
      <view class="loading-state" wx:if="{{isLoading && games.length === 0}}">
        <view class="loading-text">加载中...</view>
      </view>
    </view>
  </scroll-view>
  
  <!-- 筛选面板 -->
  <view class="filter-panel-mask" wx:if="{{showFilter}}" bindtap="hideFilterPanel"></view>
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <view class="filter-panel-header">
      <text class="filter-panel-title">筛选条件</text>
      <view class="filter-panel-close" bindtap="hideFilterPanel">×</view>
    </view>
    
    <view class="filter-section">
      <view class="filter-section-title">游戏类型</view>
      <view class="filter-options">
        <view class="filter-option {{filterType === 'mahjong' ? 'active' : ''}}" bindtap="selectGameType" data-type="mahjong">麻将</view>
        <view class="filter-option {{filterType === 'poker' ? 'active' : ''}}" bindtap="selectGameType" data-type="poker">扑克</view>
        <view class="filter-option {{filterType === 'board' ? 'active' : ''}}" bindtap="selectGameType" data-type="board">桌游</view>
        <view class="filter-option {{filterType === 'other' ? 'active' : ''}}" bindtap="selectGameType" data-type="other">其他</view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-section-title">日期范围</view>
      <view class="filter-date-range">
        <picker mode="date" value="{{startDate}}" start="2020-01-01" end="{{endDate || '2030-12-31'}}" bindchange="bindStartDateChange">
          <view class="date-picker {{startDate ? 'has-value' : ''}}">
            {{startDate || '开始日期'}}
          </view>
        </picker>
        <text class="date-separator">至</text>
        <picker mode="date" value="{{endDate}}" start="{{startDate || '2020-01-01'}}" end="2030-12-31" bindchange="bindEndDateChange">
          <view class="date-picker {{endDate ? 'has-value' : ''}}">
            {{endDate || '结束日期'}}
          </view>
        </picker>
      </view>
    </view>
    
    <view class="filter-actions">
      <button class="btn-reset" bindtap="resetFilter">重置</button>
      <button class="btn-apply" bindtap="applyFilter">应用</button>
    </view>
  </view>
</view>