// index.js
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({
    data: {
        userInfo: null,
        hasUserInfo: false,
        appTitle: '分分记'
    },
    onLoad() {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        const statusBarHeight = systemInfo.statusBarHeight;
        this.setData({
            statusBarHeight
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
            title: '分分记'
        });

        // 开发阶段：检查本地存储中是否有session，如果有则直接跳转
        const session = wx.getStorageSync('session');
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                const now = Date.now();

                if (sessionData && sessionData.expireTime > now) {
                    // session有效，直接使用
                    this.setData({
                        hasUserInfo: true,
                        userInfo: sessionData.userInfo
                    });

                    console.log('发现有效session，直接跳转到游戏列表');
                    // 跳转到游戏列表页
                    setTimeout(() => {
                        router.switchTab('gameList');
                    }, 500);
                    return;
                } else {
                    // session过期，清除本地存储
                    wx.removeStorageSync('session');
                    console.log('session已过期，已清除');
                }
            } catch (e) {
                // session数据格式错误，清除
                wx.removeStorageSync('session');
                console.log('session数据格式错误，已清除');
            }
        }
    },

    // 开始使用按钮点击事件
    navigateToGameList() {
        console.log('点击了开始使用按钮');

        // 开发阶段：跳过鉴权，直接进行无感登录并跳转
        this.performSilentLogin();
    },

    // 无感登录方法
    performSilentLogin() {
        console.log('执行无感登录...');

        // 生成默认用户信息（开发阶段使用）
        const defaultUserInfo = {
            nickName: '开发用户' + Math.floor(Math.random() * 1000),
            avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
            gender: 0
        };

        // 模拟登录成功的session数据（开发阶段使用）
        const mockSessionData = {
            token: 'dev_token_' + Date.now(),
            openid: 'dev_openid_' + Math.floor(Math.random() * 100000),
            expireTime: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7天后过期
            userInfo: defaultUserInfo
        };

        // 存储session到本地
        wx.setStorageSync('session', JSON.stringify(mockSessionData));

        // 更新用户信息状态
        this.setData({
            hasUserInfo: true,
            userInfo: mockSessionData.userInfo
        });

        console.log('无感登录成功:', defaultUserInfo.nickName);

        // 记录用户已经使用过小程序
        wx.setStorageSync('hasUserInfo', true);

        // 直接跳转到游戏列表页
        router.switchTab('gameList');
    }
})