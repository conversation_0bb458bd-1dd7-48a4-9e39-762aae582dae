<!--index.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>
  
  <!-- 状态栏 -->
  <view class="status-bar"></view>
  
  <!-- 欢迎页内容 -->
  <view class="welcome-content">
    <view class="logo-container">
      <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
    </view>
    
    <view class="title-container">
      <view class="mahjong-title-box">
        <text class="title">分分记</text>
      </view>
      <text class="subtitle">轻松记录每一局游戏分数</text>
    </view>
    
    <view class="features">
      <view class="feature-item">
        <view class="feature-icon">
          <view class="mahjong-icon">簿</view>
        </view>
        <view class="feature-text">
          <text class="feature-title">简单记分</text>
          <text class="feature-desc">一键记录各种游戏分数</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">
          <view class="mahjong-icon">算</view>
        </view>
        <view class="feature-text">
          <text class="feature-title">实时结算</text>
          <text class="feature-desc">随时查看游戏总分情况</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">
          <view class="mahjong-icon">历</view>
        </view>
        <view class="feature-text">
          <text class="feature-title">历史记录</text>
          <text class="feature-desc">查看以往游戏数据分析</text>
        </view>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="btn btn-primary btn-block" bindtap="navigateToGameList">
        开始使用
      </button>
    </view>
  </view>
</view>
