/**index.wxss**/
/* 页面根元素样式 - 与设计标准保持一致 */
page {
  background-color: #f8f4e9 !important; /* 统一米色背景 */
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

/* 背景麻将装饰元素 - 与设计标准保持一致 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx;
  margin-top: 44px; /* 状态栏高度 */
  position: relative;
  z-index: 10;
}

.logo-container {
  margin: 64rpx 0 32rpx;
  position: relative;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04); /* 统一阴影效果 */
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 64rpx;
}

.mahjong-title-box {
  position: relative;
  padding: 12rpx 20rpx; /* 与设计标准保持一致 */
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
  transform: rotate(-2deg);
}

.title {
  font-size: 56rpx;
  font-weight: 600;
  color: #8B0000; /* 统一深红色 */
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.2);
  letter-spacing: 6rpx;
  font-family: "Microsoft YaHei", sans-serif;
}

.subtitle {
  font-size: 28rpx;
  color: #666; /* 统一次要文字颜色 */
  margin-top: 12rpx;
}

.features {
  width: 100%;
  margin-bottom: 64rpx;
}

/* 应用统一的卡片设计标准 */
.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx; /* 统一间距 */
  padding: 32rpx; /* 统一内边距 */
  border-radius: 16rpx; /* 统一圆角 */
  background-color: #ffffff; /* 统一背景色 */
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04); /* 统一阴影 */
  border-left: 8rpx solid #d4af37; /* 统一金色边框 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 统一过渡效果 */
}

.feature-item:active {
  transform: scale(0.98); /* 统一点击效果 */
  background-color: rgba(248, 244, 233, 0.3);
}

.feature-icon {
  margin-right: 24rpx;
}

.mahjong-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000; /* 统一深红色 */
  font-size: 40rpx;
  font-weight: bold;
  border-radius: 16rpx; /* 统一圆角 */
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.feature-text {
  display: flex;
  flex-direction: column;
}

.feature-title {
  font-size: 32rpx; /* 统一标题字体 */
  font-weight: 500;
  color: #333; /* 统一主要文字颜色 */
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx; /* 统一说明文字字体 */
  color: #666; /* 统一次要文字颜色 */
}

.action-buttons {
  width: 100%;
  padding: 0 32rpx;
  margin-top: 32rpx;
}

/* 应用统一的按钮设计标准 */
.action-buttons .btn {
  height: 96rpx;
  font-size: 32rpx; /* 调整字体大小 */
  border-radius: 32rpx; /* 保持圆润的按钮样式 */
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000; /* 统一深红色 */
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3); /* 统一阴影效果 */
  border: none;
  letter-spacing: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 统一过渡效果 */
}

.action-buttons .btn:before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
  transform: rotate(15deg) translate(-100%, 0);
  transition: transform 0.6s ease;
}

.action-buttons .btn:active:before {
  transform: rotate(15deg) translate(100%, 0);
}

/* 响应式设计 - 与设计标准保持一致 */
@media screen and (max-width: 600rpx) {
  .welcome-content {
    padding: 24rpx;
  }

  .feature-item {
    padding: 24rpx;
    margin-bottom: 24rpx;
  }

  .feature-title {
    font-size: 28rpx;
  }

  .feature-desc {
    font-size: 22rpx;
  }

  .mahjong-icon {
    width: 64rpx;
    height: 64rpx;
    font-size: 32rpx;
  }

  .action-buttons .btn {
    height: 80rpx;
    font-size: 28rpx;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1000rpx) {
  .welcome-content {
    max-width: 800rpx;
    margin: 0 auto;
    padding: 40rpx;
  }

  .features {
    max-width: 600rpx;
  }
}
