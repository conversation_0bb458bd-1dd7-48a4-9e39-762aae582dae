// pages/navbar-demo/index.js
Page({
  data: {
    navbarHeight: 88,
    statusBarHeight: 20,
    pageStackLength: 1,
    currentTheme: 'light',
    
    // 导航栏配置
    showBack: true,
    showHome: true,
    showCapsule: true,
    showRightSlot: false,
    
    // 主题配置
    navbarBackground: '#ffffff',
    navbarColor: '#333333',
    
    themes: {
      light: {
        background: '#ffffff',
        color: '#333333'
      },
      dark: {
        background: '#1a1a1a',
        color: '#ffffff'
      },
      blue: {
        background: '#1296db',
        color: '#ffffff'
      }
    }
  },

  onLoad() {
    // 获取页面栈信息
    const pages = getCurrentPages();
    this.setData({
      pageStackLength: pages.length
    });
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 获取导航栏高度
    this.getNavbarHeight();
  },

  // 获取导航栏高度
  getNavbarHeight() {
    setTimeout(() => {
      try {
        const navbar = this.selectComponent('custom-navbar');
        if (navbar) {
          const height = navbar.getNavbarHeight();
          this.setData({
            navbarHeight: height
          });
          console.log('获取到导航栏高度:', height);
        }
      } catch (error) {
        console.error('获取导航栏高度失败:', error);
      }
    }, 100);
  },

  // 导航栏返回事件
  onNavBack(e) {
    console.log('导航栏返回事件:', e.detail);
    wx.showToast({
      title: '返回按钮被点击',
      icon: 'none',
      duration: 1500
    });
  },

  // 导航栏首页事件
  onNavHome(e) {
    console.log('导航栏首页事件:', e.detail);
    wx.showToast({
      title: '首页按钮被点击',
      icon: 'none',
      duration: 1500
    });
  },

  // 切换返回按钮显示
  toggleBack(e) {
    this.setData({
      showBack: e.detail.value
    });
  },

  // 切换首页按钮显示
  toggleHome(e) {
    this.setData({
      showHome: e.detail.value
    });
  },

  // 切换胶囊显示
  toggleCapsule(e) {
    this.setData({
      showCapsule: e.detail.value
    });
  },

  // 切换右侧插槽显示
  toggleRightSlot(e) {
    this.setData({
      showRightSlot: e.detail.value
    });
  },

  // 更换主题
  changeTheme(e) {
    const theme = e.currentTarget.dataset.theme;
    const themeConfig = this.data.themes[theme];
    
    if (themeConfig) {
      this.setData({
        currentTheme: theme,
        navbarBackground: themeConfig.background,
        navbarColor: themeConfig.color
      });
      
      wx.showToast({
        title: `已切换到${theme}主题`,
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 右侧按钮点击事件
  handleRightAction() {
    wx.showToast({
      title: '右侧按钮被点击',
      icon: 'none',
      duration: 1500
    });
  }
});
