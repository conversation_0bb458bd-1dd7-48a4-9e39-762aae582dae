<!--navbar-demo.wxml-->
<view class="container">
  <!-- 动态胶囊导航 -->
  <custom-navbar
    title="胶囊导航示例"
    background="{{navbarBackground}}"
    color="{{navbarColor}}"
    show-back="{{showBack}}"
    show-home="{{showHome}}"
    show-capsule="{{showCapsule}}"
    show-right-slot="{{showRightSlot}}"
    bind:back="onNavBack"
    bind:home="onNavHome"
  >
    <view slot="right" wx:if="{{showRightSlot}}" bindtap="handleRightAction">
      <text style="color: {{navbarColor}}; font-size: 28rpx;">保存</text>
    </view>
  </custom-navbar>
  
  <!-- 内容区域 -->
  <scroll-view class="content" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px);">
    <view class="demo-section">
      <view class="section-title">胶囊导航组件演示</view>
      
      <view class="demo-item">
        <view class="demo-title">✅ 当前页面效果</view>
        <view class="demo-desc">完整的胶囊导航，包含返回和首页按钮</view>
      </view>
      
      <view class="demo-item">
        <view class="demo-title">🎯 主要特性</view>
        <view class="demo-list">
          <text class="demo-point">• 胶囊状设计，与微信原生按钮对齐</text>
          <text class="demo-point">• 自适应不同设备的状态栏高度</text>
          <text class="demo-point">• 智能返回逻辑，处理页面栈边界情况</text>
          <text class="demo-point">• 支持 tabBar 页面的首页跳转</text>
          <text class="demo-point">• 深色模式自动适配</text>
          <text class="demo-point">• 高度可复用，配置灵活</text>
        </view>
      </view>
      
      <view class="demo-item">
        <view class="demo-title">🔧 配置选项</view>
        <view class="config-grid">
          <view class="config-item">
            <text class="config-label">显示返回</text>
            <switch checked="{{showBack}}" bindchange="toggleBack" />
          </view>
          <view class="config-item">
            <text class="config-label">显示首页</text>
            <switch checked="{{showHome}}" bindchange="toggleHome" />
          </view>
          <view class="config-item">
            <text class="config-label">显示胶囊</text>
            <switch checked="{{showCapsule}}" bindchange="toggleCapsule" />
          </view>
          <view class="config-item">
            <text class="config-label">右侧插槽</text>
            <switch checked="{{showRightSlot}}" bindchange="toggleRightSlot" />
          </view>
        </view>
      </view>
      
      <view class="demo-item">
        <view class="demo-title">📱 使用方法</view>
        <view class="code-block">
          <text class="code-text">&lt;custom-navbar</text>
          <text class="code-text">  title="页面标题"</text>
          <text class="code-text">  show-back="{{true}}"</text>
          <text class="code-text">  show-home="{{true}}"</text>
          <text class="code-text">  bind:back="onNavBack"</text>
          <text class="code-text">  bind:home="onNavHome"</text>
          <text class="code-text">&gt;&lt;/custom-navbar&gt;</text>
        </view>
      </view>
      
      <view class="demo-item">
        <view class="demo-title">🎨 样式定制</view>
        <view class="style-options">
          <button class="style-btn" bindtap="changeTheme" data-theme="light">浅色主题</button>
          <button class="style-btn" bindtap="changeTheme" data-theme="dark">深色主题</button>
          <button class="style-btn" bindtap="changeTheme" data-theme="blue">蓝色主题</button>
        </view>
      </view>
      
      <view class="demo-item">
        <view class="demo-title">📊 组件信息</view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">导航栏高度</text>
            <text class="info-value">{{navbarHeight}}px</text>
          </view>
          <view class="info-item">
            <text class="info-label">状态栏高度</text>
            <text class="info-value">{{statusBarHeight}}px</text>
          </view>
          <view class="info-item">
            <text class="info-label">页面栈深度</text>
            <text class="info-value">{{pageStackLength}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">当前主题</text>
            <text class="info-value">{{currentTheme}}</text>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
