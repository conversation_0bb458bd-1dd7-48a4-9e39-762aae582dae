/* pages/navbar-demo/index.wxss */
.container {
  height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 32rpx;
}

.demo-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 32rpx;
}

.demo-item {
  margin-bottom: 40rpx;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.demo-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.demo-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.demo-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.demo-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.demo-point {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 配置网格 */
.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.config-label {
  font-size: 28rpx;
  color: #333;
}

/* 代码块 */
.code-block {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 6rpx solid #1296db;
}

.code-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
  margin-bottom: 4rpx;
}

/* 样式选项 */
.style-options {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.style-btn {
  flex: 1;
  min-width: 160rpx;
  height: 72rpx;
  background-color: #1296db;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.style-btn:active {
  background-color: #0e7bb8;
  transform: scale(0.98);
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  text-align: center;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1296db;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .style-options {
    flex-direction: column;
  }
  
  .style-btn {
    min-width: auto;
  }
}
