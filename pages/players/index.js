// players.js
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({
    data: {
        statusBarHeight: 20,
        gameInfo: null,
        players: [],
        colorOptions: [
            { name: '蓝色', value: '#1296db' },
            { name: '绿色', value: '#52c41a' },
            { name: '紫色', value: '#722ed1' },
            { name: '红色', value: '#f5222d' },
            { name: '橙色', value: '#fa8c16' },
            { name: '青色', value: '#13c2c2' },
            { name: '粉色', value: '#eb2f96' },
            { name: '黄色', value: '#fadb14' }
        ],
        selectedColorIndex: 0,
        playerName: '',
        newPlayerName: '',
        playerCount: 4,
        initialScore: 0,
        canConfirm: false,
        canAddPlayer: false,
        canCreateGame: false,
        isCreating: false
    },

    onLoad() {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        // 从本地存储获取游戏信息
        const gameInfo = wx.getStorageSync('currentGameInfo');
        if (gameInfo) {
            // 设置初始玩家名称
            const nextPlayerName = `玩家${this.data.players.length + 1}`;

            this.setData({
                gameInfo: gameInfo,
                playerName: nextPlayerName,
                newPlayerName: nextPlayerName,
                playerCount: gameInfo.playerCount || 4,
                initialScore: gameInfo.initialScore || 0,
                canAddPlayer: nextPlayerName.trim() !== ''
            });
        } else {
            // 如果没有游戏信息，返回上一页
            router.navigateBack();
        }
    },

    navigateBack() {
        router.navigateBack();
    },

    onPlayerNameInput(e) {
        const value = e.detail.value;
        this.setData({
            playerName: value,
            newPlayerName: value,
            canAddPlayer: value.trim() !== ''
        });
        this.checkCanConfirm();
    },

    selectColor(e) {
        const index = e.currentTarget.dataset.index;
        this.setData({
            selectedColorIndex: index
        });
    },

    addPlayer() {
        if (!this.data.playerName.trim()) {
            wx.showToast({
                title: '请输入玩家名称',
                icon: 'none'
            });
            return;
        }

        const playerCount = this.data.gameInfo.playerCount;
        if (this.data.players.length >= playerCount) {
            wx.showToast({
                title: `最多添加${playerCount}名玩家`,
                icon: 'none'
            });
            return;
        }

        // 添加新玩家
        const newPlayer = {
            name: this.data.playerName,
            color: this.data.colorOptions[this.data.selectedColorIndex].value
        };

        const players = [...this.data.players, newPlayer];
        const nextPlayerName = `玩家${players.length + 1}`;

        this.setData({
            players,
            playerName: nextPlayerName,
            newPlayerName: nextPlayerName,
            selectedColorIndex: (this.data.selectedColorIndex + 1) % this.data.colorOptions.length,
            canAddPlayer: nextPlayerName.trim() !== ''
        });

        this.checkCanConfirm();
    },

    removePlayer(e) {
        const index = e.currentTarget.dataset.index;
        const players = [...this.data.players];
        players.splice(index, 1);
        this.setData({
            players
        });
        this.checkCanConfirm();
    },

    checkCanConfirm() {
        // 至少需要2名玩家才能开始游戏
        const canStart = this.data.players.length >= 2;
        this.setData({
            canConfirm: canStart,
            canCreateGame: canStart
        });
    },

    createGame() {
        if (this.data.isCreating) return;

        if (!this.data.canConfirm) {
            wx.showToast({
                title: '至少需要2名玩家',
                icon: 'none'
            });
            return;
        }

        this.setData({ isCreating: true });

        // 显示加载中提示
        wx.showLoading({
            title: '创建游戏中...',
            mask: true
        });

        // 构建游戏数据
        const gameData = {
            name: this.data.gameInfo.name,
            type: this.data.gameInfo.type,
            mode: this.data.gameInfo.mode,
            playerCount: this.data.players.length,
            initialScore: this.data.gameInfo.initialScore,
            players: this.data.players
        };

        // 调用API创建游戏
        api.games.create(gameData).then(res => {
            wx.hideLoading();
            console.log('创建游戏成功:', res);

            // 清除临时存储的游戏信息
            wx.removeStorageSync('currentGameInfo');

            // 显示成功提示
            wx.showToast({
                title: '创建成功',
                icon: 'success',
                duration: 1500
            });

            // 跳转到记分页面
            setTimeout(() => {
                router.redirectTo('score', { id: res.data.id });
            }, 1500);
        }).catch(err => {
            wx.hideLoading();
            console.error('创建游戏失败:', err);

            wx.showToast({
                title: '创建失败: ' + (err.message || '未知错误'),
                icon: 'none',
                duration: 2000
            });

            this.setData({ isCreating: false });
        });
    }
})