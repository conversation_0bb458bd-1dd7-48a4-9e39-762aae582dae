<!--players.wxml-->
<view class="container">
  <!-- 背景麻将装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>

  <!-- 自定义导航栏 -->
  <custom-navbar
    title="添加玩家"
    show-back="{{true}}"
    show-home="{{true}}"
  ></custom-navbar>
  
  <!-- 内容区域 -->
  <view class="content">
    <view class="form-card">
      <view class="form-header">
        <text class="mahjong-section-title">已添加 {{players.length}}/{{playerCount}} 位玩家</text>
      </view>
      
      <!-- 提示标签移到这里 -->
      <view class="tips inner-tips">
        <text class="tip-text">提示：所有玩家初始分数为 {{initialScore}}</text>
      </view>
      
      <view class="add-player-form fixed-form" wx:if="{{players.length < playerCount}}">
        <view class="form-group">
          <input class="form-input" placeholder="请输入玩家名称" value="{{newPlayerName}}" bindinput="onPlayerNameInput" />
          <view class="input-btn {{canAddPlayer ? 'active' : ''}}" bindtap="addPlayer">添加</view>
        </view>
        
        <!-- 颜色选择器 -->
        <view class="color-selector">
          <text class="color-label">选择颜色：</text>
          <view class="color-options">
            <view class="color-option {{selectedColorIndex === index ? 'selected' : ''}}" 
                  wx:for="{{colorOptions}}" 
                  wx:key="name" 
                  style="background-color: {{item.value}}"
                  bindtap="selectColor"
                  data-index="{{index}}">
            </view>
          </view>
        </view>
      </view>
      
      <view class="player-list-container">
        <view class="player-list" wx:if="{{players.length > 0}}">
          <view class="player-item" wx:for="{{players}}" wx:key="index">
            <view class="player-avatar" style="background-color: {{item.color}}">
              {{item.name[0]}}
            </view>
            <view class="player-name">{{item.name}}</view>
            <view class="player-action" bindtap="removePlayer" data-index="{{index}}">
              <text class="action-icon">×</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部间距占位 -->
    <view class="bottom-spacer"></view>
  </view>
  
  <!-- 底部固定导航栏 -->
  <view class="bottom-nav-bar">
    <button class="next-btn" bindtap="createGame" disabled="{{!canCreateGame}}">开始游戏</button>
  </view>
</view> 