/**players.wxss**/
/* 页面根元素样式 - 与设计标准保持一致 */
page {
  background-color: #f8f4e9 !important; /* 统一米色背景 */
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

/* 背景麻将装饰元素 - 与设计标准保持一致 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.status-bar {
  width: 100%;
  background-color: transparent;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx;
  height: 44px;
  background-color: transparent;
  position: relative;
  z-index: 100;
}

.nav-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #8B0000; /* 深红色 */
}

.nav-back {
  width: 40rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  font-weight: 300;
  color: #8B0000; /* 深红色 */
}

.nav-placeholder {
  width: 40rpx;
}

.content {
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  position: relative;
  z-index: 1003;
}

.form-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 400rpx); /* 让卡片占据大部分可见高度 */
}

.form-header {
  font-size: 28rpx;
  color: #8B0000; /* 深红色 */
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.player-list {
  margin-bottom: 32rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.2); /* 金色边框 */
}

.player-item:last-child {
  border-bottom: none;
}

.player-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 16rpx; /* 更圆润的边角 */
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #ffffff;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.player-name {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.player-action {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-icon {
  font-size: 48rpx;
  color: #8B0000; /* 深红色 */
  line-height: 1;
}

.add-player-form {
  margin-top: 24rpx;
}

.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.form-input {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx 0 0 12rpx;
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  padding: 0 28rpx;
  font-size: 28rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid #d4af37; /* 金色边框 */
  border-right: none;
  color: #333;
}

.input-btn {
  width: 120rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #cccccc;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 0 12rpx 12rpx 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-btn.active {
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变 */
  color: #8B0000; /* 深红色 */
}

/* 颜色选择器 */
.color-selector {
  margin-top: 16rpx;
  background-color: rgba(248, 244, 233, 0.6);
  padding: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.2);
}

.color-label {
  font-size: 28rpx;
  color: #8B0000; /* 深红色 */
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

.color-options {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  margin: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.color-option.selected::after {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border: 4rpx solid #d4af37; /* 金色边框 */
  border-radius: 20rpx;
}

.tips {
  margin-bottom: 32rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(212, 175, 55, 0.2));
  border: 1rpx solid #d4af37;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.15);
}

.tip-text {
  font-size: 26rpx;
  color: #8B0000; /* 深红色 */
  padding-left: 16rpx;
}

.action-buttons {
  padding: 0 32rpx;
}

.btn {
  height: 96rpx;
  line-height: 96rpx;
  font-size: 32rpx;
  border-radius: 16rpx;
  border: none;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3); /* 金色阴影 */
}

.btn-primary {
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变 */
  color: #8B0000; /* 深红色 */
}

.btn-block {
  width: 100%;
}

button[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

/* 固定表单样式 */
.fixed-form {
  position: relative;
  z-index: 10;
  background-color: white;
  margin-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.3);
  padding-bottom: 16rpx;
}

.player-list-container {
  flex: 1;
  margin-top: 24rpx;
  min-height: 300rpx; /* 增加最小高度 */
  max-height: calc(100vh - 420rpx); /* 调整最大高度以避免溢出 */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.player-list {
  margin-top: 16rpx;
  padding-top: 8rpx;
}

/* 底部间距 */
.bottom-spacer {
  height: 120rpx;  /* 增加高度以避免内容被底部导航栏遮挡 */
}

/* 底部固定导航栏 */
.bottom-nav-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx; /* 调整高度 */
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  box-sizing: border-box;
  z-index: 1005;  /* 确保显示在最上层 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.next-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变 */
  color: #8B0000; /* 深红色 */
  border-radius: 16rpx; /* 统一圆角，与参考标准保持一致 */
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果，与参考标准保持一致 */
  letter-spacing: 4rpx;
  position: relative;
  overflow: hidden;
  padding: 24rpx; /* 统一内边距，与参考标准保持一致 */
}

.next-btn:active {
  transform: scale(0.98); /* 统一点击效果，与参考标准保持一致 */
  background-color: rgba(230, 196, 108, 0.25);
}

.next-btn[disabled] {
  background: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

/* 内部提示标签样式 */
.inner-tips {
  margin: 8rpx 0 24rpx 0;
  padding: 12rpx;
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.15), rgba(212, 175, 55, 0.15));
  border: 1rpx solid rgba(212, 175, 55, 0.3);
  border-radius: 10rpx;
}

.inner-tips .tip-text {
  font-size: 24rpx;
  color: #8B0000; /* 深红色 */
  padding-left: 10rpx;
} 