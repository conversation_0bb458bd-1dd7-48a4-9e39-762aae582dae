// pages/score/index.js
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({

    /**
     * 页面的初始数据
     */
    data: {
        statusBarHeight: 20,
        navbarHeight: 88, // 自定义导航栏高度
        game: null,
        gameId: '',
        gameMode: 'local', // 游戏模式：local(本地) / invite(邀请好友)
        roundCount: 0,
        initialScore: 0, // 初始分数
        isLoading: false, // 加载状态

        // 玩家选择
        selectedPlayers: [], // 当前选中的玩家索引数组
        currentPlayer: -1, // 当前选中的玩家索引（兼容旧版本）
        playerSelectedMap: {}, // 玩家选中状态映射 {0: true, 1: false, 2: true}
        multiSelectMode: true, // 是否开启多选模式，默认开启

        // 快速记分
        currentMode: 'win', // 默认使用"赢"模式
        scoreChips: [1, 2, 5, 10], // 快速分数选项
        customScore: '', // 自定义分数值 - 初始为空字符串
        customDirection: '', // 用于标记在自定义模式下选择的方向（'win'或'lose'）

        // 本局得分
        currentRoundScores: [], // 当前局每个玩家的分数变化
        hasScoreChanges: false, // 是否有分数变化
        totalRoundScore: 0, // 当前局分数总和
        scoreBalanced: true, // 分数是否平衡

        // 添加玩家功能
        showAddPlayerModal: false, // 是否显示添加玩家弹窗
        newPlayerName: '', // 新玩家昵称
        canAddNewPlayer: false, // 是否可以添加新玩家
        selectedColorIndex: 0, // 选中的颜色索引
        colorOptions: [{
                name: '蓝色',
                value: '#1296db'
            },
            {
                name: '绿色',
                value: '#52c41a'
            },
            {
                name: '紫色',
                value: '#722ed1'
            },
            {
                name: '红色',
                value: '#f5222d'
            },
            {
                name: '橙色',
                value: '#fa8c16'
            },
            {
                name: '青色',
                value: '#13c2c2'
            },
            {
                name: '粉色',
                value: '#eb2f96'
            },
            {
                name: '黄色',
                value: '#fadb14'
            }
        ],

        // 弹窗控制
        showOptionsMenu: false,
        showRoundDetail: false,
        selectedRound: 0,

        // 历史记录折叠控制
        allRoundsCollapsed: false, // 一键折叠所有记录的状态
        fromHistory: false // 标记是否从历史记录页面进入
    },


    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log('页面加载，参数:', options);

        // 强制设置页面样式，防止跳转时的视觉bug
        this.forcePageStyle();

        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();

        // 恢复一键折叠状态
        let allRoundsCollapsed = false;
        try {
            allRoundsCollapsed = wx.getStorageSync('allRoundsCollapsed') || false;
        } catch (error) {
            console.error('获取一键折叠状态失败:', error);
        }

        // 恢复多选模式状态
        let multiSelectMode = true; // 默认为多选模式
        try {
            const storedMultiSelectMode = wx.getStorageSync('multiSelectMode');
            // 只有当存储中有明确的false值时才设为false
            if (storedMultiSelectMode === false) {
                multiSelectMode = false;
            }
        } catch (error) {
            console.error('获取多选模式状态失败:', error);
        }

        // 确保selectedPlayers初始化为空数组
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            statusBarHeight: systemInfo.statusBarHeight,
            selectedPlayers: [], // 确保初始化为空数组
            currentPlayer: -1, // 重置当前选中玩家
            allRoundsCollapsed: allRoundsCollapsed,
            multiSelectMode: multiSelectMode
        });

        if (options.id) {
            console.log('获取到游戏ID:', options.id);
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                gameId: options.id
            }, () => {
                // 确保gameId设置完成后再加载游戏
                this.loadGame();
            });
        } else {
            console.error('未获取到游戏ID');
            wx.showToast({
                title: '缺少游戏ID',
                icon: 'none'
            });
            setTimeout(() => {
                wx.navigateBack();
            }, 1000);
        }
    },

    // 强制设置页面样式，防止跳转时的视觉bug
    forcePageStyle() {
        try {
            // 强制设置页面背景色
            if (wx.setBackgroundColor) {
                wx.setBackgroundColor({
                    backgroundColor: '#f5f7fa',
                    backgroundColorTop: '#f5f7fa',
                    backgroundColorBottom: '#f5f7fa'
                });
            }
        } catch (error) {
            console.error('设置页面样式失败:', error);
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {
        console.log('页面初次渲染完成');
        console.log('当前游戏对象:', this.data.game);

        // 如果游戏对象为空，尝试重新加载
        if (!this.data.game && this.data.gameId) {
            console.log('游戏对象为空，尝试重新加载');
            this.loadGame();
        }
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        console.log('页面显示，验证选中状态...');

        // 再次强制设置页面样式
        this.forcePageStyle();
        this.checkGameValid();

        // 确保玩家选中状态正确
        this.setData({
            selectedPlayers: this.data.selectedPlayers || [],
            _forceUpdate: Date.now() // 添加时间戳强制更新
        });
    },

    // 检查游戏对象是否有效
    checkGameValid() {
        const {
            game,
            gameId
        } = this.data;

        if (!game && gameId) {
            console.log('游戏对象无效，尝试重新加载');
            this.loadGame();
            return;
        }

        if (game && (!game.players || game.players.length === 0)) {
            console.error('游戏对象有效，但玩家数据无效');
            wx.showToast({
                title: '玩家数据无效',
                icon: 'none'
            });
        }
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        this.loadGame();
        wx.stopPullDownRefresh();
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    loadGame() {
        const {
            gameId
        } = this.data;
        console.log('正在加载游戏，ID:', gameId);

        if (this.data.isLoading) return;

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            isLoading: true,
            selectedPlayers: [], // 重置选中玩家状态
            currentPlayer: -1 // 重置当前选中玩家
        });

        // 显示加载中提示
        wx.showLoading({
            title: '加载中...',
        });

        // 调用API获取游戏详情
        api.games.detail(gameId).then(res => {
            wx.hideLoading();
            console.log('获取游戏详情成功:', res);

            const game = res.data;

            // 检查玩家数据
            if (!game.players || game.players.length === 0) {
                console.error('游戏没有玩家数据');
                wx.showToast({
                    title: '游戏没有玩家数据',
                    icon: 'none'
                });
                this.setData({
                    _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                    isLoading: false
                });
                return;
            }

            // 格式化轮次时间和分数数据
            if (game.rounds && game.rounds.length > 0) {
                // 按时间倒序排列（最新的在前面）
                game.rounds.sort((a, b) => new Date(b.time) - new Date(a.time));

                game.rounds.forEach(round => {
                    const date = new Date(round.time);
                    round.timeFormatted = this.formatTime(date);

                    // 根据一键折叠状态设置折叠状态
                    round.collapsed = this.data.allRoundsCollapsed;

                    // 初始化分数数组
                    let scoresArray = new Array(game.players.length).fill(0);

                    // 处理新的API返回格式，scores是包含playerId和score的对象数组
                    if (round.scores && Array.isArray(round.scores)) {
                        round.scores.forEach(scoreObj => {
                            // 查找playerID对应的索引
                            const playerIndex = game.players.findIndex(player => player.id === scoreObj.playerId);
                            if (playerIndex !== -1) {
                                scoresArray[playerIndex] = scoreObj.score;
                            }
                        });
                    }

                    // 替换原始scores为处理后的数组
                    round.scores = scoresArray;
                });
            }

            // 获取初始分数
            const initialScore = game.initialScore || 0;

            // 初始化当前轮次的分数数组
            const currentRoundScores = Array(game.players.length).fill(0);

            console.log('加载游戏:', game);
            console.log('玩家信息:', game.players);

            // 为玩家添加选中状态属性
            if (game.players && game.players.length > 0) {
                game.players = game.players.map(player => ({
                    ...player,
                    selected: false // 添加选中状态属性
                }));
            }

            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                game: game,
                initialScore,
                gameMode: game.mode || 'local', // 设置游戏模式
                roundCount: game.rounds ? game.rounds.length : 0,
                currentRoundScores: currentRoundScores,
                selectedPlayers: [], // 确保初始化为空数组
                currentPlayer: -1, // 重置当前选中玩家
                isLoading: false
            }, () => {
                // 在setData回调中再次检查数据是否正确设置
                console.log('游戏数据设置完成，当前game:', this.data.game);
                console.log('玩家数据:', this.data.game ? this.data.game.players : '无游戏数据');
                console.log('选中玩家数组:', this.data.selectedPlayers);
                console.log('选中玩家数组长度:', this.data.selectedPlayers.length);

                // 调用调试方法验证选中状态
                this.debugPlayerSelection();
            });

        }).catch(err => {
            wx.hideLoading();
            console.error('获取游戏详情失败:', err);

            wx.showToast({
                title: '获取游戏详情失败: ' + (err.message || '未知错误'),
                icon: 'none',
                duration: 2000
            });

            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                isLoading: false
            });

            // 如果是404错误，返回上一页
            if (err.code === 404) {
                setTimeout(() => {
                    wx.navigateBack();
                }, 1500);
            }
        });
    },

    // 导航栏高度回调
    onNavbarHeight(e) {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            navbarHeight: e.detail.height
        });
    },

    navigateBack() {
        // 如果是从历史记录页面进入，则返回历史记录页面
        if (this.data.fromHistory) {
            wx.navigateBack();
            return;
        }

        // 否则返回游戏列表页面（Tab页面）
        wx.switchTab({
            url: '/pages/gameList/index'
        }).then(() => {
            console.log('跳转到游戏列表页面成功');
        }).catch(err => {
            console.error('跳转失败:', err);
            // 如果跳转失败，显示错误提示
            wx.showToast({
                title: '跳转失败: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 2000
            });
        });
    },

    // 选择玩家（新版本-支持多选）
    // 重构：玩家选择方法 - 简化逻辑，确保状态更新可靠
    selectPlayer(e) {
        const index = parseInt(e.currentTarget.dataset.index);

        // 验证输入参数
        if (isNaN(index) || index < 0) {
            console.error('无效的玩家索引:', index);
            return;
        }

        if (!this.data.game || !this.data.game.players || index >= this.data.game.players.length) {
            console.error('游戏数据无效或玩家索引超出范围');
            return;
        }

        // 获取当前选中状态
        const currentSelected = this.data.selectedPlayers || [];
        const isCurrentlySelected = currentSelected.indexOf(index) !== -1;

        // 计算新的选中状态
        let newSelectedPlayers;

        if (this.data.multiSelectMode) {
            // 多选模式：可以选择多个玩家
            if (isCurrentlySelected) {
                // 取消选中：从数组中移除
                newSelectedPlayers = currentSelected.filter(i => i !== index);
            } else {
                // 选中：添加到数组
                newSelectedPlayers = [...currentSelected, index];
            }
        } else {
            // 单选模式：只能选择一个玩家
            if (isCurrentlySelected) {
                // 取消选中当前玩家
                newSelectedPlayers = [];
            } else {
                // 选中新玩家，取消其他玩家
                newSelectedPlayers = [index];
            }
        }

        // 构建新的选中状态映射
        const newPlayerSelectedMap = {};
        newSelectedPlayers.forEach(i => {
            newPlayerSelectedMap[i] = true;
        });

        // 更新数据并强制刷新
        this.setData({
            selectedPlayers: newSelectedPlayers,
            playerSelectedMap: newPlayerSelectedMap,
            currentPlayer: newSelectedPlayers.length === 1 ? newSelectedPlayers[0] : -1,
            _forceUpdate: Date.now() // 添加时间戳强制更新
        });
    },

    // 切换多选模式
    toggleMultiSelect() {
        const newMultiSelectMode = !this.data.multiSelectMode;

        // 如果从多选切换到单选，且当前选中了多个玩家，则只保留第一个选中的玩家
        let newSelectedPlayers = [...this.data.selectedPlayers];
        let newPlayerSelectedMap = {
            ...this.data.playerSelectedMap
        };

        if (!newMultiSelectMode && newSelectedPlayers.length > 1) {
            const firstSelectedPlayer = newSelectedPlayers[0];
            newSelectedPlayers = [firstSelectedPlayer];

            // 重建选中状态映射
            newPlayerSelectedMap = {};
            newPlayerSelectedMap[firstSelectedPlayer] = true;
        }

        this.setData({
            multiSelectMode: newMultiSelectMode,
            selectedPlayers: newSelectedPlayers,
            playerSelectedMap: newPlayerSelectedMap,
            currentPlayer: newSelectedPlayers.length === 1 ? newSelectedPlayers[0] : -1,
            _forceUpdate: Date.now() // 添加时间戳强制更新
        });

        // 保存多选模式状态到本地存储
        try {
            wx.setStorageSync('multiSelectMode', newMultiSelectMode);
        } catch (error) {
            console.error('保存多选模式状态失败:', error);
        }
    },

    // 设置记分模式
    setScoreMode(e) {
        const mode = e.currentTarget.dataset.mode;
        const {
            currentMode
        } = this.data;

        // 如果是自定义模式下再次点击自定义按钮，返回到win模式
        if (currentMode === 'custom' && mode === 'custom') {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                currentMode: 'win',
                customDirection: ''
            });
            return;
        }

        // 如果在自定义模式下点击赢或输按钮，只设置方向，不应用分数
        if (currentMode === 'custom' && (mode === 'win' || mode === 'lose')) {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                customDirection: mode
            });
            return;
        }

        // 其他情况
        if (mode === 'custom') {
            // 切换到自定义模式
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                currentMode: mode,
                customDirection: ''
            });
        } else {
            // 切换到赢/输模式
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                currentMode: mode,
                customScore: '',
                customDirection: ''
            });
        }
    },

    // 应用快速分数（支持多个玩家）
    applyQuickScore(e) {
        const {
            currentRoundScores,
            game,
            currentMode
        } = this.data;

        // 确保selectedPlayers是一个数组
        const selectedPlayers = Array.isArray(this.data.selectedPlayers) ? this.data.selectedPlayers : [];

        // 检查是否选择了玩家
        if (!selectedPlayers.length) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        if (!game || !game.players || !game.players.length) {
            wx.showToast({
                title: '玩家数据无效',
                icon: 'none'
            });
            return;
        }

        // 获取点击的分数值
        let score = Number(e.currentTarget.dataset.score);

        // 在"输"模式下，将分数转换为负数
        if (currentMode === 'lose') {
            score = -score;
        }

        const updatedScores = [...currentRoundScores];

        // 给所有选中的玩家添加分数
        selectedPlayers.forEach(playerIndex => {
            if (playerIndex >= 0 && playerIndex < game.players.length) {
                updatedScores[playerIndex] += score;
            }
        });

        this.updateCurrentRoundScores(updatedScores);
    },

    // 自定义分数输入
    onCustomScoreInput(e) {
        // 获取输入值，可能是空字符串或数字字符串
        const inputValue = e.detail.value;

        // 不要在这里处理转换，保持原始输入
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: inputValue
        });
    },

    // 增加自定义分数
    incrementScore() {
        const {
            customScore,
            selectedPlayers
        } = this.data;

        // 如果没有选择玩家，不执行操作
        if (!selectedPlayers || selectedPlayers.length === 0) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        // 处理空字符串或非数字输入
        const currentScore = customScore === '' || isNaN(Number(customScore)) ? 0 : Number(customScore);

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: (currentScore + 1).toString()
        });
    },

    // 减少自定义分数
    decrementScore() {
        const {
            customScore,
            selectedPlayers
        } = this.data;

        // 如果没有选择玩家，不执行操作
        if (!selectedPlayers || selectedPlayers.length === 0) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        // 处理空字符串或非数字输入
        const currentScore = customScore === '' || isNaN(Number(customScore)) ? 0 : Number(customScore);

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: (currentScore - 1).toString()
        });
    },

    // 应用自定义分数（支持多个玩家）
    applyCustomScore() {
        const {
            currentRoundScores,
            customScore,
            customDirection,
            game
        } = this.data;

        // 确保selectedPlayers是一个数组
        const selectedPlayers = Array.isArray(this.data.selectedPlayers) ? this.data.selectedPlayers : [];

        // 检查是否选择了玩家
        if (!selectedPlayers.length) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        // 检查是否选择了方向（赢或输）
        if (!customDirection) {
            wx.showToast({
                title: '请先点击赢或输',
                icon: 'none'
            });
            return;
        }

        // 检查分数是否为空
        if (customScore === '') {
            wx.showToast({
                title: '请输入分数',
                icon: 'none'
            });
            return;
        }

        // 将分数转换为数字
        const scoreNum = Number(customScore);

        // 检查是否为有效数字
        if (isNaN(scoreNum)) {
            wx.showToast({
                title: '请输入有效分数',
                icon: 'none'
            });
            return;
        }

        // 检查游戏和玩家数据有效性
        if (!game || !game.players || !game.players.length) {
            wx.showToast({
                title: '玩家数据无效',
                icon: 'none'
            });
            return;
        }

        // 根据方向计算实际分数（赢为正，输为负）
        const actualScore = scoreNum * (customDirection === 'win' ? 1 : -1);

        // 更新选中的玩家分数
        const updatedScores = [...currentRoundScores];
        selectedPlayers.forEach(playerIndex => {
            if (playerIndex >= 0 && playerIndex < game.players.length) {
                updatedScores[playerIndex] += actualScore;
            }
        });

        this.updateCurrentRoundScores(updatedScores);

        // 清空自定义分数，但保持方向选择
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: ''
        });

        // 显示提示
        wx.showToast({
            title: '已应用分数',
            icon: 'success',
            duration: 1000
        });
    },

    // 更新当前局分数
    updateCurrentRoundScores(scores) {
        // 计算总分
        let totalScore = 0;
        scores.forEach(score => {
            totalScore += Number(score) || 0;
        });

        const hasScoreChanges = scores.some(score => score !== 0);

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            currentRoundScores: scores,
            totalRoundScore: totalScore,
            scoreBalanced: Math.abs(totalScore) < 0.01, // 考虑浮点数精度问题
            hasScoreChanges
        });
    },

    // 重置当前局
    resetCurrentRound() {
        const {
            game
        } = this.data;
        if (!game) return;

        const currentRoundScores = new Array(game.players.length).fill(0);

        // 只重置分数，不清除玩家选择
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            currentRoundScores,
            totalRoundScore: 0,
            scoreBalanced: true,
            hasScoreChanges: false,
            customScore: ''
        });
    },

    // 提交分数
    submitScores() {
        const {
            game,
            currentRoundScores,
            totalRoundScore,
            scoreBalanced
        } = this.data;

        if (!game) {
            wx.showToast({
                title: '游戏数据无效',
                icon: 'none'
            });
            return;
        }

        if (!currentRoundScores.some(score => score !== 0)) {
            wx.showToast({
                title: '请先输入分数',
                icon: 'none'
            });
            return;
        }

        if (!scoreBalanced && Math.abs(totalRoundScore) > 0.01) {
            wx.showModal({
                title: '分数不平衡',
                content: `当前总分为${totalRoundScore}，确定要继续提交吗？`,
                success: (res) => {
                    if (res.confirm) {
                        this.doSubmitScores();
                    }
                }
            });
        } else {
            this.doSubmitScores();
        }
    },

    // 实际提交分数（API + 本地存储）
    doSubmitScores() {
        const {
            game,
            currentRoundScores
        } = this.data;

        // 显示加载状态
        wx.showLoading({
            title: '保存中...',
            mask: true
        });

        // 构建API请求数据
        const scoresData = game.players.map((player, index) => ({
            playerId: player.id || player._id || `player_${index}`,
            score: currentRoundScores[index]
        }));

        console.log('提交分数数据到API:', scoresData);

        // 调用API提交分数
        api.games.submitScore(game.id, scoresData).then(res => {
            console.log('API提交分数成功:', res);

            // 处理API响应，更新本地数据
            this.handleSubmitScoreSuccess(res.data, currentRoundScores);

            wx.hideLoading();
            wx.showToast({
                title: '保存成功',
                icon: 'success'
            });

        }).catch(err => {
            console.error('API提交分数失败:', err);
            wx.hideLoading();

            // 根据错误类型显示不同的提示
            let errorMessage = '保存失败';
            if (err.code === 400) {
                errorMessage = '参数错误，请检查分数数据';
            } else if (err.code === 401) {
                errorMessage = '登录已过期，请重新登录';
            } else if (err.code === 404) {
                errorMessage = '游戏不存在';
            } else if (err.code === 500) {
                errorMessage = '服务器错误，请稍后重试';
            } else if (err.code === -1) {
                errorMessage = '网络连接失败';
            } else if (err.message) {
                errorMessage = err.message;
            }

            wx.showModal({
                title: '保存失败',
                content: `${errorMessage}\n\n是否保存到本地？`,
                confirmText: '保存到本地',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        // 保存到本地
                        this.saveScoresToLocal(currentRoundScores);
                    }
                }
            });
        });
    },

    // 处理API提交成功的响应
    handleSubmitScoreSuccess(apiData, currentRoundScores) {
        const {
            game
        } = this.data;

        // 创建新的轮次记录
        const newRound = {
            id: apiData.roundId || `round_${Date.now()}`,
            time: new Date(apiData.time || Date.now()).toISOString(),
            timeFormatted: this.formatTime(new Date(apiData.time || Date.now())),
            scores: [...currentRoundScores],
            collapsed: false // 新轮次默认展开
        };

        // 更新游戏数据
        const updatedGame = {
            ...game
        };

        // 初始化 rounds 数组（如果不存在）
        if (!updatedGame.rounds) {
            updatedGame.rounds = [];
        }

        // 将新轮次添加到开头（最新的在前面）
        updatedGame.rounds.unshift(newRound);

        // 如果API返回了更新后的玩家数据，使用API数据；否则本地计算
        if (apiData.players && Array.isArray(apiData.players)) {
            updatedGame.players = apiData.players.map(apiPlayer => {
                const localPlayer = game.players.find(p => p.id === apiPlayer.id);
                return {
                    ...localPlayer,
                    ...apiPlayer,
                    selected: false // 重置选中状态
                };
            });
        } else {
            // 本地计算玩家分数
            updatedGame.players = updatedGame.players.map((player, index) => ({
                ...player,
                score: (player.score || this.data.initialScore) + currentRoundScores[index],
                selected: false // 重置选中状态
            }));
        }

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 如果添加新轮次后不是所有记录都折叠，则更新一键折叠状态
        const allCollapsed = updatedGame.rounds.every(round => round.collapsed);
        if (!allCollapsed && this.data.allRoundsCollapsed) {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                allRoundsCollapsed: false
            });
            try {
                wx.setStorageSync('allRoundsCollapsed', false);
            } catch (error) {
                console.error('保存一键折叠状态失败:', error);
            }
        }

        // 更新页面数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            selectedPlayers: [], // 重置选中玩家
            currentPlayer: -1
        });

        // 重置当前轮次分数
        this.resetCurrentRound();
    },

    // 保存分数到本地（API失败时的备选方案）
    saveScoresToLocal(currentRoundScores) {
        const {
            game
        } = this.data;

        // 创建新的轮次记录
        const newRound = {
            id: `round_${Date.now()}`,
            time: new Date().toISOString(),
            timeFormatted: this.formatTime(new Date()),
            scores: [...currentRoundScores],
            collapsed: false // 新轮次默认展开
        };

        // 更新游戏数据
        const updatedGame = {
            ...game
        };

        // 初始化 rounds 数组（如果不存在）
        if (!updatedGame.rounds) {
            updatedGame.rounds = [];
        }

        // 将新轮次添加到开头（最新的在前面）
        updatedGame.rounds.unshift(newRound);

        // 更新玩家分数
        updatedGame.players = updatedGame.players.map((player, index) => ({
            ...player,
            score: (player.score || this.data.initialScore) + currentRoundScores[index],
            selected: false // 重置选中状态
        }));

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 如果添加新轮次后不是所有记录都折叠，则更新一键折叠状态
        const allCollapsed = updatedGame.rounds.every(round => round.collapsed);
        if (!allCollapsed && this.data.allRoundsCollapsed) {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                allRoundsCollapsed: false
            });
            try {
                wx.setStorageSync('allRoundsCollapsed', false);
            } catch (error) {
                console.error('保存一键折叠状态失败:', error);
            }
        }

        // 更新页面数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            selectedPlayers: [], // 重置选中玩家
            currentPlayer: -1
        });

        // 重置当前轮次分数
        this.resetCurrentRound();

        wx.showToast({
            title: '已保存到本地',
            icon: 'success'
        });
    },

    // 保存游戏数据到本地存储
    saveGameToLocal(gameData) {
        try {
            // 获取本地存储的游戏列表
            const localGames = wx.getStorageSync('localGames') || {};

            // 更新当前游戏数据
            localGames[gameData.id] = {
                ...gameData,
                lastModified: new Date().toISOString(),
                needsSync: true // 标记需要同步到服务器
            };

            // 保存到本地存储
            wx.setStorageSync('localGames', localGames);

            console.log('游戏数据已保存到本地存储:', gameData.id);
        } catch (error) {
            console.error('保存游戏数据到本地存储失败:', error);
            wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000
            });
        }
    },

    // 从本地存储加载游戏数据
    loadGameFromLocal(gameId) {
        try {
            const localGames = wx.getStorageSync('localGames') || {};
            return localGames[gameId] || null;
        } catch (error) {
            console.error('从本地存储加载游戏数据失败:', error);
            return null;
        }
    },

    // 提交所有本地数据到服务器
    syncLocalDataToServer() {
        try {
            const localGames = wx.getStorageSync('localGames') || {};
            const needsSyncGames = Object.values(localGames).filter(game => game.needsSync);

            if (needsSyncGames.length === 0) {
                console.log('没有需要同步的数据');
                return Promise.resolve();
            }

            console.log('开始同步本地数据到服务器，共', needsSyncGames.length, '个游戏');

            // 这里可以实现批量同步逻辑
            // 暂时跳过实际的API调用
            return Promise.resolve();
        } catch (error) {
            console.error('同步本地数据到服务器失败:', error);
            return Promise.reject(error);
        }
    },

    formatTime(date) {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const hour = date.getHours();
        const minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
        return `${month}月${day}日 ${hour}:${minute}`;
    },

    showOptions() {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showOptionsMenu: true
        });
    },

    hideOptions() {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showOptionsMenu: false
        });
    },

    stopPropagation() {
        // 阻止事件冒泡
    },

    showEndGameConfirm() {
        // 隐藏选项菜单
        this.hideOptions();

        // 显示结束游戏确认弹窗
        this.confirmEndGame();
    },

    navigateToHistory() {
        // 隐藏选项菜单
        this.hideOptions();

        // 跳转到历史记录页面（Tab页面）
        wx.switchTab({
            url: '/pages/history/index'
        }).then(() => {
            console.log('跳转到历史记录页面成功');
        }).catch(err => {
            console.error('跳转失败:', err);
            // 如果跳转失败，显示错误提示
            wx.showToast({
                title: '跳转失败: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 2000
            });
        });
    },

    // 重构：调试和验证方法
    debugPlayerSelection() {
        console.log('=== 玩家选中状态调试信息 ===');
        console.log('selectedPlayers数组:', this.data.selectedPlayers);
        console.log('selectedPlayers类型:', typeof this.data.selectedPlayers);
        console.log('selectedPlayers长度:', this.data.selectedPlayers ? this.data.selectedPlayers.length : 'undefined');

        if (this.data.game && this.data.game.players) {
            console.log('游戏玩家总数:', this.data.game.players.length);
            this.data.game.players.forEach((player, index) => {
                const isSelected = this.data.selectedPlayers && this.data.selectedPlayers.indexOf(index) !== -1;
                const cssClass = isSelected ? 'player-card-compact active' : 'player-card-compact';
                console.log(`玩家${index} "${player.name}": ${isSelected ? '✓选中' : '○未选中'} (CSS: ${cssClass})`);
            });
        }
        console.log('=== 调试信息结束 ===');
    },

    // 强制样式更新方法
    forceStyleUpdate() {
        // 通过微任务强制重新渲染
        setTimeout(() => {
            this.setData({
                _styleUpdate: Date.now()
            });
        }, 10);
    },

    // 手动测试选中状态的方法（可在控制台调用）
    testSelection() {
        console.log('=== 手动测试选中状态 ===');

        // 测试选中第一个玩家
        if (this.data.game && this.data.game.players && this.data.game.players.length > 0) {
            console.log('测试选中第一个玩家...');
            this.setData({
                selectedPlayers: [0]
            }, () => {
                this.debugPlayerSelection();
                this.forceStyleUpdate();

                // 2秒后测试取消选中
                setTimeout(() => {
                    console.log('测试取消选中...');
                    this.setData({
                        selectedPlayers: []
                    }, () => {
                        this.debugPlayerSelection();
                        this.forceStyleUpdate();
                    });
                }, 2000);
            });
        }
    },

    // 清除所有选中状态的方法
    clearAllSelection() {
        console.log('清除所有选中状态...');
        this.setData({
            selectedPlayers: [],
            currentPlayer: -1,
            _forceUpdate: Date.now()
        }, () => {
            this.debugPlayerSelection();
            this.forceStyleUpdate();
        });
    },

    navigateToSettlement() {
        const {
            game
        } = this.data;

        if (!game) {
            wx.showToast({
                title: '游戏数据无效',
                icon: 'none'
            });
            return;
        }

        if (this.data.isLoading) return;

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            isLoading: true
        });

        // 显示加载中提示
        wx.showLoading({
            title: '结算中...',
            mask: true
        });

        // 调用API结算游戏
        api.games.settle(game.id).then(res => {
            wx.hideLoading();
            console.log('结算游戏成功:', res);

            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                isLoading: false
            });

            // 隐藏选项菜单
            this.hideOptions();

            // 跳转到历史记录页面
            wx.switchTab({
                url: '/pages/history/index',
                success: () => {
                    console.log('成功跳转到历史记录页面');
                },
                fail: (error) => {
                    console.error('跳转失败:', error);
                    // 跳转失败时回退到游戏列表
                    wx.switchTab({
                        url: '/pages/gameList/index'
                    });
                }
            });
        }).catch(err => {
            wx.hideLoading();
            console.error('结算游戏失败:', err);

            wx.showToast({
                title: '结算失败: ' + (err.message || '未知错误'),
                icon: 'none',
                duration: 2000
            });

            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                isLoading: false
            });
        });
    },

    deleteGame() {
        const {
            game
        } = this.data;

        if (!game) {
            wx.showToast({
                title: '游戏数据无效',
                icon: 'none'
            });
            return;
        }

        if (this.data.isLoading) return;

        // 显示确认对话框
        wx.showModal({
            title: '确认删除',
            content: `确定要删除游戏"${game.name}"吗？此操作不可恢复。`,
            confirmColor: '#ff4d4f',
            success: (res) => {
                if (res.confirm) {
                    this.setData({
                        _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                        isLoading: true
                    });

                    // 显示加载中提示
                    wx.showLoading({
                        title: '删除中...',
                        mask: true
                    });

                    // 调用API删除游戏
                    api.games.delete(game.id).then(res => {
                        wx.hideLoading();
                        console.log('删除游戏成功:', res);

                        // 显示成功提示
                        wx.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });

                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            isLoading: false
                        });

                        // 返回到游戏列表页
                        setTimeout(() => {
                            wx.switchTab({
                                url: '/pages/gameList/index'
                            }).then(() => {
                                console.log('跳转到游戏列表页面成功');
                            }).catch(err => {
                                console.error('跳转失败:', err);
                            });
                        }, 1500);
                    }).catch(err => {
                        wx.hideLoading();
                        console.error('删除游戏失败:', err);

                        wx.showToast({
                            title: '删除失败: ' + (err.message || '未知错误'),
                            icon: 'none',
                            duration: 2000
                        });

                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            isLoading: false
                        });
                    });
                }
            }
        });
    },

    viewRoundDetail(e) {
        const index = e.currentTarget.dataset.index;

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showRoundDetail: true,
            selectedRound: index
        });
    },

    hideRoundDetail() {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showRoundDetail: false
        });
    },

    // 切换历史记录折叠状态
    toggleRoundCollapse(e) {
        const index = e.currentTarget.dataset.index;
        const {
            game,
            allRoundsCollapsed
        } = this.data;

        if (!game || !game.rounds || index < 0 || index >= game.rounds.length) {
            return;
        }

        // 防止快速连续点击同一个按钮
        const toggleKey = `toggle_${index}`;
        if (this[toggleKey]) {
            return;
        }
        this[toggleKey] = true;

        // 复制游戏数据
        const updatedGame = {
            ...game
        };
        updatedGame.rounds = [...game.rounds];

        // 切换指定轮次的折叠状态
        updatedGame.rounds[index] = {
            ...game.rounds[index],
            collapsed: !game.rounds[index].collapsed
        };

        // 检查是否所有记录都是同一状态，如果是则更新一键折叠状态
        const allCollapsed = updatedGame.rounds.every(round => round.collapsed);
        const allExpanded = updatedGame.rounds.every(round => !round.collapsed);

        let newAllRoundsCollapsed = allRoundsCollapsed;
        if (allCollapsed) {
            newAllRoundsCollapsed = true;
        } else if (allExpanded) {
            newAllRoundsCollapsed = false;
        }

        // 更新数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            allRoundsCollapsed: newAllRoundsCollapsed
        });

        // 同时更新本地存储
        this.saveGameToLocal(updatedGame);

        // 保存一键折叠状态
        if (newAllRoundsCollapsed !== allRoundsCollapsed) {
            try {
                wx.setStorageSync('allRoundsCollapsed', newAllRoundsCollapsed);
            } catch (error) {
                console.error('保存一键折叠状态失败:', error);
            }
        }

        // 动画完成后解除锁定
        setTimeout(() => {
            this[toggleKey] = false;
        }, 450); // 略长于动画时长
    },

    // 一键折叠/展开所有历史记录
    toggleAllRoundsCollapse() {
        const {
            allRoundsCollapsed,
            game
        } = this.data;

        if (!game || !game.rounds || game.rounds.length === 0) {
            return;
        }

        // 防止快速连续点击
        if (this.isToggling) {
            return;
        }
        this.isToggling = true;

        const newAllRoundsCollapsed = !allRoundsCollapsed;

        // 复制游戏数据
        const updatedGame = {
            ...game
        };
        updatedGame.rounds = [...game.rounds];

        // 根据一键折叠状态更新所有轮次的折叠状态
        updatedGame.rounds = updatedGame.rounds.map(round => ({
            ...round,
            collapsed: newAllRoundsCollapsed
        }));

        // 更新数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            allRoundsCollapsed: newAllRoundsCollapsed
        });

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 保存一键折叠状态到本地存储
        try {
            wx.setStorageSync('allRoundsCollapsed', newAllRoundsCollapsed);
        } catch (error) {
            console.error('保存一键折叠状态失败:', error);
        }

        // 动画完成后解除锁定
        setTimeout(() => {
            this.isToggling = false;
        }, 500); // 略长于动画时长
    },

    deleteRound() {
        const {
            game,
            selectedRound
        } = this.data;

        if (!game || !game.rounds || selectedRound < 0 || selectedRound >= game.rounds.length) {
            wx.showToast({
                title: '无效的轮次',
                icon: 'none'
            });
            this.hideRoundDetail();
            return;
        }

        const round = game.rounds[selectedRound];

        if (this.data.isLoading) return;

        // 显示确认对话框
        wx.showModal({
            title: '确认删除',
            content: '确定要删除这个轮次吗？此操作不可恢复。',
            confirmColor: '#ff4d4f',
            success: (res) => {
                if (res.confirm) {
                    this.setData({
                        _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                        isLoading: true
                    });

                    // 显示加载中提示
                    wx.showLoading({
                        title: '删除中...',
                        mask: true
                    });

                    // 调用API删除轮次
                    api.games.deleteRound(game.id, round.id).then(res => {
                        wx.hideLoading();
                        console.log('删除轮次成功:', res);

                        // 隐藏轮次详情弹窗
                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            showRoundDetail: false,
                            isLoading: false
                        });

                        // 显示成功提示
                        wx.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });

                        // 重新加载游戏数据
                        setTimeout(() => {
                            this.loadGame();
                        }, 500);
                    }).catch(err => {
                        wx.hideLoading();
                        console.error('删除轮次失败:', err);

                        wx.showToast({
                            title: '删除失败: ' + (err.message || '未知错误'),
                            icon: 'none',
                            duration: 2000
                        });

                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            isLoading: false
                        });
                    });
                }
            }
        });
    },

    /**
     * 重新加载游戏
     */
    reloadGame() {
        wx.showLoading({
            title: '加载中...'
        });

        // 清除当前游戏数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: null,
            roundCount: 0,
            currentRoundScores: [],
            selectedPlayers: []
        });

        // 重新加载游戏
        setTimeout(() => {
            this.loadGame();
            wx.hideLoading();
        }, 500);
    },

    /**
     * 确认结束游戏
     */
    confirmEndGame() {
        wx.showModal({
            title: '结束游戏',
            content: '确定要结束本次游戏？',
            confirmColor: '#f5222d',
            success: (res) => {
                if (res.confirm) {
                    // 获取当前游戏数据
                    const {
                        game
                    } = this.data;

                    if (!game) {
                        wx.showToast({
                            title: '游戏数据无效',
                            icon: 'none'
                        });
                        return;
                    }

                    // 结束游戏并调用结算API
                    this.endGameAndSettle(game);
                }
                // 如果取消，则继续停留在记分页面，不需要额外处理
            }
        });
    },

    /**
     * 结束游戏并调用结算API
     */
    endGameAndSettle(game) {
        console.log('开始结束游戏并调用结算API:', game.id);

        // 显示结算中的加载提示
        wx.showLoading({
            title: '结算中...',
            mask: true
        });

        // 调用游戏结算API
        api.games.settle(game.id).then(res => {
            console.log('游戏结算API调用成功:', res);
            wx.hideLoading();

            // 显示结算完成提示
            wx.showToast({
                title: '结算完成',
                icon: 'success',
                duration: 1500
            });

            // 标记游戏为已结束并保存到本地
            const finalGameData = {
                ...game,
                status: 'completed',
                endTime: new Date().toISOString(),
                settlementData: res.data // 保存结算数据
            };
            this.saveGameToLocal(finalGameData);

            // 延迟跳转到结算页面
            setTimeout(() => {
                this.navigateToSettlementPage(game.id);
            }, 1500);

        }).catch(err => {
            console.error('游戏结算API调用失败:', err);
            wx.hideLoading();

            // 根据错误类型显示不同的提示
            let errorMessage = '结算失败';
            let showRetryOption = true;

            if (err.code === 400) {
                errorMessage = '参数错误，请检查游戏数据';
                showRetryOption = false;
            } else if (err.code === 401) {
                errorMessage = '登录已过期，请重新登录';
                showRetryOption = false;
            } else if (err.code === 404) {
                errorMessage = '游戏不存在';
                showRetryOption = false;
            } else if (err.code === 500) {
                errorMessage = '服务器错误，请稍后重试';
            } else if (err.code === -1) {
                errorMessage = '网络连接失败，请检查网络';
            } else if (err.message) {
                errorMessage = err.message;
            }

            // 显示错误处理弹窗
            this.showSettleErrorDialog(errorMessage, showRetryOption, game);
        });
    },

    /**
     * 显示结算错误处理弹窗
     */
    showSettleErrorDialog(errorMessage, showRetryOption, game) {
        const buttons = [];

        if (showRetryOption) {
            buttons.push({
                text: '重试',
                action: () => this.endGameAndSettle(game)
            });
        }

        buttons.push({
            text: '本地结算',
            action: () => this.settleGameLocally(game)
        });

        buttons.push({
            text: '取消',
            action: () => {} // 什么都不做，继续游戏
        });

        // 构建弹窗内容
        let content = errorMessage;
        if (showRetryOption) {
            content += '\n\n您可以重试结算，或选择本地结算继续查看结果。';
        } else {
            content += '\n\n您可以选择本地结算继续查看结果。';
        }

        wx.showModal({
            title: '结算失败',
            content: content,
            showCancel: true,
            confirmText: showRetryOption ? '重试' : '本地结算',
            cancelText: '取消',
            confirmColor: '#1296db',
            success: (res) => {
                if (res.confirm) {
                    if (showRetryOption) {
                        // 重试结算
                        this.endGameAndSettle(game);
                    } else {
                        // 本地结算
                        this.settleGameLocally(game);
                    }
                } else if (res.cancel) {
                    // 用户选择取消，继续游戏
                    console.log('用户取消结算，继续游戏');
                }
            }
        });
    },

    /**
     * 本地结算游戏（API失败时的备选方案）
     */
    settleGameLocally(game) {
        console.log('执行本地结算:', game.id);

        wx.showLoading({
            title: '本地结算中...',
            mask: true
        });

        // 模拟本地结算处理
        setTimeout(() => {
            wx.hideLoading();

            // 标记游戏为已结束并保存到本地
            const finalGameData = {
                ...game,
                status: 'completed',
                endTime: new Date().toISOString(),
                settledLocally: true // 标记为本地结算
            };
            this.saveGameToLocal(finalGameData);

            wx.showToast({
                title: '本地结算完成',
                icon: 'success',
                duration: 1500
            });

            // 跳转到结算页面
            setTimeout(() => {
                this.navigateToSettlementPage(game.id);
            }, 1500);
        }, 800);
    },

    /**
     * 跳转到历史记录页面
     */
    navigateToSettlementPage(gameId) {
        // 跳转到结算页面
        wx.navigateTo({
            url: `/pages/history/gameHistory?id=${gameId}`
        }).then(() => {
            console.log('跳转到游戏历史页面成功');
        }).catch(err => {
            console.error('跳转失败:', err);
            // 如果跳转失败，显示错误提示
            wx.showToast({
                title: '跳转失败: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 2000
            });

            // 跳转失败时回退到游戏列表
            setTimeout(() => {
                wx.switchTab({
                    url: '/pages/gameList/index'
                });
            }, 2000);
        });
    },

    /**
     * 提交游戏数据到服务器（保留原有方法，用于其他功能）
     */
    submitGameToServer(gameData) {
        return new Promise((resolve, reject) => {
            // 检查是否有可用的API接口
            if (!api || !api.games || !api.games.submitScore) {
                console.log('API接口不可用，跳过服务器提交');
                reject(new Error('API接口不可用'));
                return;
            }

            // 这里可以实现实际的服务器提交逻辑
            // 暂时模拟提交过程
            setTimeout(() => {
                // 模拟网络请求
                const shouldSucceed = Math.random() > 0.3; // 70% 成功率

                if (shouldSucceed) {
                    console.log('模拟服务器提交成功');
                    resolve();
                } else {
                    console.log('模拟服务器提交失败');
                    reject(new Error('网络连接失败'));
                }
            }, 1000);
        });
    },

    /**
     * 生成游戏战绩页面
     */
    generateGameResults(game) {
        // 标记游戏为已结束
        const updatedGame = {
            ...game,
            status: 'completed',
            endTime: Date.now()
        };

        // 更新存储
        const games = wx.getStorageSync('games') || [];
        const updatedGames = games.map(g => g.id === game.id ? updatedGame : g);
        wx.setStorageSync('games', updatedGames);

        // 显示加载提示
        wx.showLoading({
            title: '生成战绩中...'
        });

        // 延迟一小段时间后跳转到战绩页面
        setTimeout(() => {
            wx.hideLoading();

            // 跳转到历史记录页面
            wx.switchTab({
                url: '/pages/history/index',
                success: () => {
                    console.log('成功跳转到历史记录页面');
                },
                fail: (error) => {
                    console.error('跳转失败:', error);
                    // 跳转失败时回退到游戏列表
                    wx.switchTab({
                        url: '/pages/gameList/index'
                    });
                }
            });
        }, 500);
    },

    // ==================== 添加玩家功能 ====================

    /**
     * 显示添加玩家弹窗
     */
    showAddPlayerDialog() {
        // 获取已使用的颜色
        const usedColors = this.data.game.players.map(player => player.color);

        // 找到第一个未使用的颜色
        let availableColorIndex = 0;
        for (let i = 0; i < this.data.colorOptions.length; i++) {
            if (!usedColors.includes(this.data.colorOptions[i].value)) {
                availableColorIndex = i;
                break;
            }
        }

        // 生成默认玩家名称
        const defaultName = `玩家${this.data.game.players.length + 1}`;

        this.setData({
            _selectionTimeStamp: Date.now(),
            showAddPlayerModal: true,
            newPlayerName: defaultName,
            selectedColorIndex: availableColorIndex,
            canAddNewPlayer: defaultName.trim() !== ''
        });
    },

    /**
     * 隐藏添加玩家弹窗
     */
    hideAddPlayerDialog() {
        this.setData({
            _selectionTimeStamp: Date.now(),
            showAddPlayerModal: false,
            newPlayerName: '',
            canAddNewPlayer: false,
            selectedColorIndex: 0
        });
    },

    /**
     * 阻止事件冒泡（防止弹窗意外关闭）
     */
    stopPropagation() {
        // 空函数，用于阻止事件冒泡
        // 在微信小程序中，catchtap 会自动阻止事件冒泡
        return false;
    },

    /**
     * 处理新玩家昵称输入
     */
    onNewPlayerNameInput(e) {
        const value = e.detail.value;
        this.setData({
            _selectionTimeStamp: Date.now(),
            newPlayerName: value,
            canAddNewPlayer: value.trim() !== ''
        });
    },

    /**
     * 选择颜色
     */
    selectColor(e) {
        const index = e.currentTarget.dataset.index;
        this.setData({
            _selectionTimeStamp: Date.now(),
            selectedColorIndex: index
        });
    },

    /**
     * 确认添加玩家
     */
    confirmAddPlayer() {
        if (!this.data.canAddNewPlayer) {
            wx.showToast({
                title: '请输入玩家昵称',
                icon: 'none'
            });
            return;
        }

        const {
            game,
            newPlayerName,
            selectedColorIndex,
            colorOptions,
            initialScore
        } = this.data;

        // 检查昵称是否重复
        const existingNames = game.players.map(player => player.name);
        if (existingNames.includes(newPlayerName.trim())) {
            wx.showToast({
                title: '玩家昵称已存在',
                icon: 'none'
            });
            return;
        }

        // 创建新玩家对象
        const newPlayer = {
            id: `player_${Date.now()}`, // 生成唯一ID
            name: newPlayerName.trim(),
            color: colorOptions[selectedColorIndex].value,
            score: initialScore, // 使用游戏的初始分数
            selected: false
        };

        // 更新游戏数据
        const updatedGame = {
            ...game,
            players: [...game.players, newPlayer]
        };

        // 更新当前局分数数组，为新玩家添加0分
        const updatedCurrentRoundScores = [...this.data.currentRoundScores, 0];

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 更新页面数据
        this.setData({
            _selectionTimeStamp: Date.now(),
            game: updatedGame,
            currentRoundScores: updatedCurrentRoundScores,
            showAddPlayerModal: false,
            newPlayerName: '',
            canAddNewPlayer: false,
            selectedColorIndex: 0
        });

        wx.showToast({
            title: '添加成功',
            icon: 'success'
        });

        console.log('新玩家添加成功:', newPlayer);
        console.log('更新后的游戏数据:', updatedGame);
    }
})