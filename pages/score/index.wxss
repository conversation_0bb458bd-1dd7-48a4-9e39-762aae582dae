/* pages/score/index.wxss */
/**score.wxss**/

/* 页面根元素样式 - 与设计标准保持一致 */
page {
  background-color: #f8f4e9 !important; /* 统一米色背景 */
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

/* 背景麻将装饰元素 - 与设计标准保持一致 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

/* 页面主体覆盖层 */
.page-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: #f8f4e9 !important; /* 与首页匹配的米色背景 */
  z-index: 999 !important;
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏右侧操作按钮 */
.nav-action {
  width: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 36rpx;
  font-weight: 600;
  color: #8B0000; /* 更改为深红色 */
}

/* 标题样式 */
.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 8rpx;
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
  transform: rotate(-2deg);
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

/* 邀请模式提示 */
.invite-mode-tip {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(212, 175, 55, 0.2));
  border: 1rpx solid #d4af37;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.15);
}

.tip-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.tip-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tip-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #8B0000;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 添加滚动视图样式 */
.score-scroll-view {
  width: 100%;
  box-sizing: border-box;
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  position: relative;
  z-index: 1002;
}

.content {
  padding: 32rpx;
  padding-top: 16rpx; /* 减少顶部内边距 */
  padding-bottom: 120rpx; /* 确保底部有足够空间 */
  margin-top: 0 !important; /* 覆盖全局样式的 margin-top: 88px */
  flex: none !important; /* 覆盖全局样式的 flex: 1 */
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  position: relative;
  z-index: 1003;
  min-height: calc(100vh - 200rpx);
}

/* 底部占位区域 */
.bottom-space {
  height: 160rpx;
}

/* 添加玩家按钮样式 - 遵循统一设计标准 */
.add-player-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  margin: 8rpx;
  border-radius: 16rpx; /* 统一圆角 */
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  border: 2rpx dashed #d4af37; /* 金色虚线边框 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 140rpx;
  cursor: pointer;
}

.add-player-item:active {
  background-color: rgba(230, 196, 108, 0.25); /* 统一点击效果 */
  border-color: #d4af37;
  transform: scale(0.98);
}

.add-player-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 统一金色渐变 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
}

.add-icon {
  font-size: 48rpx;
  color: #8B0000; /* 统一深红色 */
  font-weight: 300;
  line-height: 1;
}

.add-player-text {
  font-size: 24rpx; /* 统一说明文字字体 */
  color: #8B0000; /* 统一深红色 */
  font-weight: 500;
}

/* 底部固定操作栏 */
.bottom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 10001;
}

/* 固定底部导航栏 */
.fixed-bottom-nav {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background-color: #ffffff !important;
  padding: 24rpx 32rpx !important;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom)) !important;
  border-top: 1rpx solid #e8e8e8 !important;
  box-shadow: 0 -8rpx 24rpx rgba(0,0,0,0.15) !important;
  z-index: 9999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  backdrop-filter: blur(10rpx) !important;
  -webkit-backdrop-filter: blur(10rpx) !important;
}

.end-game-btn {
  background-color: #ff4d4f;
  color: white;
  font-size: 32rpx;
  border-radius: 20rpx;
  padding: 0 40rpx;
  height: 96rpx;
  line-height: 96rpx;
  border: none;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.35);
  transition: all 0.3s ease;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.end-game-btn:hover,
.end-game-btn:active {
  background-color: #ff7875;
  box-shadow: 0 12rpx 32rpx rgba(255, 77, 79, 0.45);
}

/* 玩家分数区域 */
.player-section {
  margin-top: 0;
  margin-bottom: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.form-title {
  margin-bottom: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 多选控制样式 */
.multi-select-control {
  display: flex;
  align-items: center;
}

.multi-select-label {
  font-size: 26rpx;
  color: #8B0000;
  margin-right: 8rpx;
}

/* 玩家网格布局 - 完全按照createGame样式 */
.game-types {
  display: flex;
  flex-wrap: nowrap;
  margin: 0 -4rpx;
  overflow-x: auto;
}

/* 完全复制createGame页面的game-type-item样式 */
.game-type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 8rpx);
  margin: 0 4rpx;
  padding: 18rpx 8rpx;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  flex-shrink: 0;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

.game-type-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12);
}

.game-type-item.active {
  border-color: #d4af37;
  box-shadow: inset 0 0 0 1rpx #d4af37, 0 8rpx 24rpx rgba(212, 175, 55, 0.2);
  transform: scale(0.98);
  background: linear-gradient(135deg, #e6c46c, #d4af37) !important;
}

.game-type-item.active .type-name {
  color: #8B0000;
  font-weight: 600;
}

.type-icon {
  font-size: 34rpx;
  margin-bottom: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.type-name {
  font-size: 18rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

.game-type-item.active .type-name {
  color: #8B0000;
  font-weight: 600;
}

.player-score {
  font-size: 30rpx;
  font-weight: 600;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.player-score.positive {
  color: #52c41a;
}

.player-score.negative {
  color: #ff4d4f;
}

.game-type-item.active .player-score {
  color: #8B0000;
}

/* 快速记分区域 */
.quick-score-section {
  margin-bottom: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.quick-score-buttons {
  margin-bottom: 24rpx;
}

.quick-score-row {
  display: flex;
  margin-bottom: 16rpx;
}

.quick-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(248, 244, 233, 0.6);
  border-radius: 16rpx;
  margin: 0 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

/* 快速按钮点击反馈效果 */
.quick-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.quick-btn:first-child {
  margin-left: 0;
}

.quick-btn:last-child {
  margin-right: 0;
}

.quick-btn.active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

.score-chip {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(248, 244, 233, 0.6);
  border-radius: 16rpx;
  margin: 0 8rpx;
  font-size: 30rpx;
  color: #333;
  border: 1rpx solid #d4af37;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

/* 分数按钮点击反馈效果 */
.score-chip:active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-color: #d4af37;
  color: #8B0000;
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.2);
}

.score-chip.disabled {
  opacity: 0.5;
  pointer-events: none;
  background-color: #eaeaea;
  border-color: #ddd;
  color: #999;
}

/* 本局得分预览 */
.round-preview {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.btn {
  border-radius: 16rpx;
  font-size: 28rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border: none;
  padding: 0 30rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

.btn-outline {
  background-color: #ffffff;
  color: #8B0000;
  border: 1rpx solid #d4af37;
}

.preview-submit-btn {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 600;
  height: 68rpx;
  min-width: 140rpx;
  padding: 0 28rpx;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(212, 175, 55, 0.35), 0 2rpx 8rpx rgba(212, 175, 55, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

/* 历史记录 */
.round-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

/* 固定底部提交按钮 */
.fixed-submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e8e8e8;
  box-shadow: 0 -8rpx 24rpx rgba(0,0,0,0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.submit-btn {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 700;
  height: 96rpx;
  min-width: 200rpx;
  padding: 0 40rpx;
  border: none;
  box-shadow: 0 16rpx 40rpx rgba(212, 175, 55, 0.45), 0 4rpx 16rpx rgba(212, 175, 55, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 1rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

/* 选项菜单弹窗 */
.options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.options-menu {
  width: 75%;
  background-color: #ffffff;
  border-radius: 24rpx 0 0 24rpx;
  overflow: hidden;
  box-shadow: -8rpx 0 40rpx rgba(0, 0, 0, 0.2);
  margin-top: 120rpx;
  max-height: 70%;
  display: flex;
  flex-direction: column;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变背景 */
}

.options-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #8B0000; /* 深红色 */
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  text-align: center;
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #8B0000;
  margin-bottom: 24rpx;
}

/* ========== Collapse Switch ========== */
.switch-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 24rpx;
  overflow: hidden;
  border-radius: 18rpx; /* 确保容器也有圆角 */
  -webkit-tap-highlight-color: transparent; /* 移除点击时的蓝色高亮 */
  position: relative; /* 确保伪元素定位正确 */
}

/* 添加一个透明的遮罩层，防止系统默认的点击高亮 */
.switch-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1;
}

.switch-hover {
  opacity: 0.8;
}

.switch {
  width: 64rpx;
  height: 36rpx;
  border-radius: 18rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  position: relative;
  transition: background 0.3s;
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.15);
  display: flex;
  align-items: center;
  overflow: hidden; /* 确保内部元素不会超出圆角边界 */
  z-index: 2; /* 确保开关在遮罩层上方 */
}

.switch-off {
  background: #e0e0e0;
}

.switch-handle {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: left 0.3s;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.handle-on {
  left: 30rpx;
}

.handle-off {
  left: 2rpx;
}

.collapse-icon {
  font-size: 32rpx;
  color: #8B0000;
  transition: transform 0.3s;
  display: inline-block;
}
.collapse-icon.collapsed {
  transform: rotate(-90deg);
  opacity: 0.5;
}
.collapse-icon.expanded {
  transform: rotate(0deg);
  opacity: 1;
}

/* ========== 加减分数输入区 ========== */
.custom-score {
  margin-top: 16rpx;
}
.custom-score-input {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.08);
  padding: 16rpx 24rpx;
  border: 1rpx solid #d4af37;
  margin-bottom: 12rpx;
}
.custom-score-input.disabled {
  opacity: 0.5;
  pointer-events: none;
}
.score-input-container {
  display: flex;
  align-items: center;
  background: #f8f4e9;
  border-radius: 12rpx;
  border: 1rpx solid #e6c46c;
  margin-right: 24rpx;
}
.score-decrement, .score-increment {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #8B0000;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx;
  margin: 0 4rpx;
  cursor: pointer;
  user-select: none;
  transition: background 0.2s;
}
.score-decrement:active, .score-increment:active {
  background: #d4af37;
  color: #fff;
}
.score-input {
  width: 100rpx;
  height: 56rpx;
  border: none;
  outline: none;
  background: transparent;
  text-align: center;
  font-size: 32rpx;
  color: #8B0000;
  font-weight: 600;
}
.input-btn {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  padding: 0 32rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12rpx;
  cursor: pointer;
  transition: background 0.2s;
}
.input-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* ========== 局数详情弹窗 ========== */
.round-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.round-detail-card {
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(212, 175, 55, 0.18), 0 2rpx 8rpx rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
}
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 16rpx 32rpx;
  background: linear-gradient(135deg, #e6c46c, #f8f4e9);
}
.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #8B0000;
}
.detail-close {
  font-size: 40rpx;
  color: #8B0000;
  padding: 0 10rpx;
  cursor: pointer;
}
.detail-content {
  padding: 32rpx;
}
.detail-time {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 24rpx;
}
.detail-scores {
  margin-bottom: 24rpx;
}
.detail-score-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.detail-score-item:last-child {
  border-bottom: none;
}
.detail-player {
  display: flex;
  align-items: center;
}
.detail-avatar {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #fff;
  margin-right: 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.18);
}
.detail-name {
  font-size: 28rpx;
  color: #8B0000;
  font-weight: 500;
}
.detail-score {
  font-size: 36rpx;
  font-weight: 700;
  color: #8B0000;
  min-width: 80rpx;
  text-align: right;
}
.detail-score.positive {
  color: #52c41a;
}
.detail-score.negative {
  color: #f5222d;
}
.detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 24rpx;
  margin-top: 24rpx;
}

/* 历史记录区域 */
.history-section {
  margin-bottom: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 4rpx solid #d4af37; /* 金色边框 */
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.collapse-all-control {
  display: flex;
  align-items: center;
}

.collapse-all-label {
  font-size: 26rpx;
  color: #8B0000;
  margin-right: 8rpx;
}

.round-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.2);
  margin-bottom: 16rpx;
}

.round-header-left {
  display: flex;
  flex-direction: column;
}

.round-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B0000;
  margin-bottom: 4rpx;
}

.round-time {
  font-size: 24rpx;
  color: #999;
}

.round-collapse-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(230, 196, 108, 0.1);
  transition: all 0.3s;
}

.round-collapse-btn:active {
  background-color: rgba(230, 196, 108, 0.3);
}

/* 局分数网格 */
.round-scores-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12rpx;
  transition: all 0.3s ease;
  max-height: 500rpx;
  overflow: hidden;
}

.round-scores-grid.collapsed {
  max-height: 0;
  margin-top: 0;
  margin-bottom: 0;
  opacity: 0;
}

.round-scores-grid.expanded {
  margin-top: 16rpx;
  margin-bottom: 8rpx;
  opacity: 1;
}

.round-score-grid-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background-color: rgba(248, 244, 233, 0.6);
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.2);
  min-height: 48rpx;
}

.grid-player-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.grid-player-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  color: #ffffff;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.grid-player-name {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.grid-score-value {
  font-size: 28rpx;
  font-weight: 600;
  min-width: 80rpx;
  text-align: right;
  flex-shrink: 0;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
  height: 100% !important;
}

.grid-score-value.positive {
  color: #52c41a;
}

.grid-score-value.negative {
  color: #f5222d;
}

/* 本局得分预览样式补充 */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.2);
}

.preview-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B0000;
}

.preview-action {
  font-size: 26rpx;
  color: #8B0000;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  background-color: rgba(230, 196, 108, 0.1);
}

.preview-scores-compact {
  margin-bottom: 16rpx;
}

.preview-item-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 48rpx;
}

.preview-item-compact:last-child {
  border-bottom: none;
}

.preview-avatar-compact {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  color: #ffffff;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.preview-name-compact {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.preview-score-compact {
  font-size: 28rpx;
  font-weight: 600;
  min-width: 80rpx;
  text-align: right;
  flex-shrink: 0;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
  height: 100% !important;
}

.preview-score-compact.positive {
  color: #52c41a;
}

.preview-score-compact.negative {
  color: #f5222d;
}

.preview-footer-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(212, 175, 55, 0.2);
}

.total-score {
  font-size: 28rpx;
  font-weight: 600;
}

.total-score.balanced {
  color: #52c41a;
}

.total-score.unbalanced {
  color: #f5222d;
}

.submit-info {
  display: flex;
  flex-direction: column;
}

.submit-total {
  font-size: 32rpx;
  font-weight: 600;
}

.submit-hint {
  font-size: 24rpx;
  color: #f5222d;
}

/* 选项菜单内容 */
.options-list {
  padding: 16rpx 0;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-child {
  border-bottom: none;
}

.option-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.options-close {
  font-size: 40rpx;
  color: #8B0000;
  padding: 0 10rpx;
}

/* 添加玩家弹窗样式 - 遵循统一设计标准 */
.add-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.add-player-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 16rpx; /* 统一圆角 */
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04); /* 统一阴影效果 */
  transform: scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.add-player-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx; /* 统一内边距 */
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.2);
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.1), rgba(212, 175, 55, 0.1)); /* 金色渐变背景 */
}

.modal-title {
  font-size: 32rpx; /* 统一标题字体 */
  font-weight: 600;
  color: #8B0000; /* 统一深红色 */
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-close:active {
  background-color: rgba(212, 175, 55, 0.2);
}

.close-icon {
  font-size: 48rpx;
  color: #8B0000; /* 统一深红色 */
  line-height: 1;
}

.modal-body {
  padding: 32rpx; /* 统一内边距 */
}

.form-group {
  margin-bottom: 32rpx; /* 统一外边距 */
}

.form-label {
  font-size: 28rpx; /* 统一副标题字体 */
  color: #666; /* 统一次要文字颜色 */
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  padding: 0 28rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #d4af37; /* 金色边框 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #d4af37;
  background-color: rgba(230, 196, 108, 0.1);
}

.color-selector {
  margin-top: 16rpx;
}

.color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx; /* 统一圆角 */
  border: 3rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15); /* 统一阴影效果 */
}

.color-option.selected {
  border-color: #d4af37; /* 金色边框 */
  transform: scale(1.1);
  box-shadow: 0 6rpx 16rpx rgba(212, 175, 55, 0.3);
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  padding: 32rpx; /* 统一内边距 */
  border-top: 1rpx solid rgba(212, 175, 55, 0.2);
  gap: 16rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx; /* 统一圆角 */
  font-size: 28rpx; /* 统一副标题字体 */
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
}

.btn-cancel {
  background-color: rgba(248, 244, 233, 0.6); /* 米色背景 */
  color: #666; /* 统一次要文字颜色 */
  border: 1rpx solid rgba(212, 175, 55, 0.3);
}

.btn-cancel:active {
  background-color: rgba(212, 175, 55, 0.2);
}

.btn-confirm {
  background-color: #cccccc;
  color: #ffffff;
}

.btn-confirm.active {
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 统一金色渐变 */
  color: #8B0000; /* 统一深红色 */
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
}

.btn-confirm.active:active {
  transform: scale(0.98); /* 统一点击效果 */
  background-color: rgba(230, 196, 108, 0.25);
}

.btn-confirm[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 600rpx) {
  .add-player-item {
    min-height: 120rpx;
    margin: 6rpx;
    padding: 12rpx;
  }

  .add-player-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 8rpx;
  }

  .add-icon {
    font-size: 36rpx;
  }

  .add-player-text {
    font-size: 22rpx;
  }

  .modal-content {
    width: 95%;
    max-width: 500rpx;
  }

  .modal-header, .modal-body, .modal-footer {
    padding: 24rpx;
  }

  .color-option {
    width: 50rpx;
    height: 50rpx;
  }

  .color-options {
    gap: 12rpx;
  }
}

/* 响应式设计 - 大屏幕优化 */
@media (min-width: 1000rpx) {
  .add-player-item {
    min-height: 160rpx;
    margin: 12rpx;
    padding: 20rpx;
  }

  .add-player-icon {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 16rpx;
  }

  .add-icon {
    font-size: 60rpx;
  }

  .add-player-text {
    font-size: 28rpx;
  }

  .modal-content {
    max-width: 700rpx;
  }

  .modal-header, .modal-body, .modal-footer {
    padding: 40rpx;
  }

  .color-option {
    width: 80rpx;
    height: 80rpx;
  }

  .color-options {
    gap: 20rpx;
  }
}