// pages/settlement/index.js
const router = require('../../utils/router');

Page({

    /**
     * 页面的初始数据
     */
    data: {
        statusBarHeight: 20,
        game: null,
        gameId: '',
        rankedPlayers: [],
        payments: [],
        hasPayments: false
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        if (options.id) {
            this.setData({
                gameId: options.id
            });
            this.loadGame();
        } else {
            wx.navigateBack();
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage(res) {
        const { game, rankedPlayers } = this.data;

        if (!game) {
            return {
                title: '游戏战绩分享',
                path: '/pages/index/index'
            };
        }

        // 构建分享标题，包含游戏名称和玩家排名信息
        let title = `《${game.name}》战绩`;

        // 如果有排名数据，添加第一名玩家信息
        if (rankedPlayers && rankedPlayers.length > 0) {
            const winner = rankedPlayers[0];
            title += `：${winner.name} 获胜，得分 ${winner.score}`;
        }

        return {
            title: title,
            path: `/pages/gameList/index?shared=true&gameId=${game.id}`,
            imageUrl: this.generateShareImage() // 自定义分享图片，如果实现的话
        };
    },

    loadGame() {
        const { gameId } = this.data;
        const games = wx.getStorageSync('games') || [];
        const game = games.find(g => g.id === gameId);

        if (!game) {
            router.navigateBack();
            return;
        }

        // 获取初始分数，如果不存在则默认为0
        const initialScore = game.initialScore || 0;

        // 计算排名
        const rankedPlayers = [...game.players].sort((a, b) => b.score - a.score);

        // 计算支付信息
        const payments = this.calculatePayments(game.players);

        this.setData({
            game,
            initialScore,
            rankedPlayers,
            payments,
            hasPayments: payments.some(p => p.amount > 0)
        });
    },

    calculatePayments(players) {
        // 分离赢家和输家
        const winners = players.filter(p => p.score > 0);
        const losers = players.filter(p => p.score < 0);

        // 如果没有赢家或输家，不需要支付
        if (winners.length === 0 || losers.length === 0) {
            return [];
        }

        const payments = [];

        // 克隆玩家数组，以便我们可以修改它们的分数
        const tempLosers = losers.map(p => ({
            ...p
        }));

        // 对于每个赢家
        for (const winner of winners) {
            let remainingToReceive = winner.score;

            // 如果没有剩余需要收款，继续下一个赢家
            if (remainingToReceive <= 0) continue;

            // 从输家中收集款项
            for (let i = 0; i < tempLosers.length && remainingToReceive > 0; i++) {
                const loser = tempLosers[i];

                // 如果这个输家已经付清，跳过
                if (loser.score >= 0) continue;

                // 计算这个输家应该支付的金额
                const amountToPay = Math.min(remainingToReceive, Math.abs(loser.score));

                if (amountToPay > 0) {
                    // 添加到支付列表
                    payments.push({
                        from: loser.id,
                        to: winner.id,
                        amount: amountToPay
                    });

                    // 更新剩余金额
                    remainingToReceive -= amountToPay;
                    loser.score += amountToPay;
                }
            }
        }

        return payments;
    },

    getPlayerColor(playerId) {
        const {
            game
        } = this.data;
        if (!game) return '';

        const player = game.players.find(p => p.id === playerId);
        return player ? player.color : '';
    },

    getPlayerName(playerId) {
        const {
            game
        } = this.data;
        if (!game) return '';

        const player = game.players.find(p => p.id === playerId);
        return player ? player.name : '';
    },

    navigateBack() {
        router.navigateBack();
    },

    shareResults() {
        // 显示分享操作菜单
        wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage']
        });

        // 主动触发分享
        wx.showModal({
            title: '分享战绩',
            content: '请点击"分享给朋友"将战绩分享给好友',
            showCancel: false,
            confirmText: '确定'
        });
    },

    completeSettlement() {
        // 标记游戏为已结算
        const { game } = this.data;

        if (!game) return;

        const updatedGame = {
            ...game,
            settled: true,
            settleTime: Date.now()
        };

        // 更新存储
        const games = wx.getStorageSync('games') || [];
        const updatedGames = games.map(g => g.id === game.id ? updatedGame : g);
        wx.setStorageSync('games', updatedGames);

        wx.showToast({
            title: '结算完成',
            icon: 'success'
        });

        // 返回游戏列表
        setTimeout(() => {
            router.switchTab('gameList');
        }, 1500);
    },

    /**
     * 生成分享图片
     * 注意：这个函数不会真正生成图片，只是返回空，小程序会使用默认分享图
     * 要实现真正的自定义分享图，需要使用canvas绘制后保存图片
     */
    generateShareImage() {
        // 如需实现自定义分享图，需要使用canvas绘制
        // 这里暂时返回空，小程序会使用默认分享图
        return '';
    }
})