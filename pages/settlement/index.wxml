<!--pages/settlement/index.wxml-->
<view class="container">
  <!-- 状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <text class="back-icon">‹</text>
    </view>
    <view class="nav-title">结算</view>
    <view class="nav-placeholder"></view>
  </view>
  
  <!-- 内容区域 -->
  <view class="content">
    <!-- 游戏信息 -->
    <view class="game-info">
      <view class="game-name">{{game.name}}</view>
      <view class="game-meta">
        <text>共 {{game.rounds.length}} 局</text>
        <text>{{game.players.length}} 位玩家</text>
      </view>
    </view>
    
    <!-- 结算结果 -->
    <view class="settlement-result">
      <view class="section-title">结算结果</view>
      
      <view class="player-ranking">
        <view class="rank-item" wx:for="{{rankedPlayers}}" wx:key="id">
          <view class="rank-number">{{index + 1}}</view>
          <view class="player-avatar" style="background-color: {{item.color}}">
            {{item.name[0]}}
          </view>
          <view class="player-name">{{item.name}}</view>
          <view class="player-score {{item.score > initialScore ? 'positive' : (item.score < initialScore ? 'negative' : '')}}">
            {{item.score}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支付信息 -->
    <view class="payment-info">
      <view class="section-title">支付信息</view>
      
      <view class="payment-list">
        <view class="payment-item" wx:for="{{payments}}" wx:key="index" wx:if="{{item.amount > 0}}">
          <view class="payment-players">
            <view class="player-avatar small" style="background-color: {{getPlayerColor(item.from)}}">
              {{getPlayerName(item.from)[0]}}
            </view>
            <text class="payment-direction">向</text>
            <view class="player-avatar small" style="background-color: {{getPlayerColor(item.to)}}">
              {{getPlayerName(item.to)[0]}}
            </view>
          </view>
          <view class="payment-amount">支付 {{item.amount}}</view>
        </view>
      </view>
      
      <view class="no-payment" wx:if="{{payments.length === 0 || !hasPayments}}">
        <text>没有需要支付的金额</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-outline" bindtap="shareResults">
        <text class="btn-icon">📤</text>
        分享结果
      </button>
      <button class="btn btn-primary" bindtap="completeSettlement">完成结算</button>
    </view>
  </view>
</view>