/* pages/settlement/index.wxss */
/**settlement.wxss**/

/* 页面根元素样式 - 与设计标准保持一致 */
page {
  background-color: #f8f4e9 !important; /* 统一米色背景 */
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

.nav-bar {
  justify-content: space-between;
}

.nav-back {
  width: 40rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  font-weight: 300;
  color: #8B0000; /* 统一深红色 */
}

.nav-placeholder {
  width: 40rpx;
}

.section-title {
  font-size: 32rpx; /* 统一标题字体 */
  font-weight: 500;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
  color: #333; /* 统一主要文字颜色 */
}

/* 游戏信息 - 应用统一卡片设计 */
.game-info {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 统一金色边框 */
}

.game-name {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.game-meta {
  display: flex;
  font-size: 28rpx;
  color: #666;
}

.game-meta text {
  margin-right: 24rpx;
}

/* 结算结果 */
.settlement-result {
  margin-bottom: 24rpx;
}

.player-ranking {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.rank-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.rank-item:nth-child(1) .rank-number {
  background-color: #f5c518;
  color: #ffffff;
}

.rank-item:nth-child(2) .rank-number {
  background-color: #a9a9a9;
  color: #ffffff;
}

.rank-item:nth-child(3) .rank-number {
  background-color: #cd7f32;
  color: #ffffff;
}

.player-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #ffffff;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.player-avatar.small {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.player-name {
  flex: 1;
  font-size: 32rpx;
}

.player-score {
  font-size: 36rpx;
  font-weight: 600;
}

.player-score.positive {
  color: #52c41a;
}

.player-score.negative {
  color: #f5222d;
}

/* 支付信息 */
.payment-info {
  margin-bottom: 24rpx;
}

.payment-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-players {
  display: flex;
  align-items: center;
}

.payment-direction {
  margin: 0 16rpx;
  font-size: 28rpx;
  color: #666;
}

.payment-amount {
  font-size: 32rpx;
  font-weight: 500;
}

.no-payment {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx;
  margin-top: 32rpx;
}

.action-buttons .btn {
  width: 48%;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  line-height: 1;
}