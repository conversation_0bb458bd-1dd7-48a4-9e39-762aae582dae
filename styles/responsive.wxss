/* styles/responsive.wxss */
/* 全应用统一响应式设计规则 */

/* 设计标准变量 */
:root {
  /* 颜色方案 */
  --primary-gold: #d4af37;
  --primary-red: #8B0000;
  --background-beige: #f8f4e9;
  --card-white: #ffffff;
  --text-primary: #333;
  --text-secondary: #666;
  --text-light: #999;
  --success-green: #52c41a;
  --error-red: #f5222d;
  
  /* 间距规范 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  
  /* 字体大小 */
  --font-title: 32rpx;
  --font-subtitle: 28rpx;
  --font-body: 26rpx;
  --font-caption: 24rpx;
  --font-small: 22rpx;
}

/* 统一卡片样式 */
.standard-card {
  background-color: var(--card-white);
  border-radius: 16rpx;
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid var(--primary-gold);
}

.secondary-card {
  background-color: rgba(248, 244, 233, 0.4);
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 统一按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: var(--primary-red);
  border-radius: 14rpx;
  font-size: var(--font-body);
  height: 80rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-outline {
  background-color: var(--card-white);
  color: var(--primary-red);
  border: 1rpx solid var(--primary-gold);
  border-radius: 14rpx;
}

/* 统一标题样式 */
.section-title {
  position: relative;
  display: inline-block;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx;
  color: var(--primary-red);
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25);
  font-size: var(--font-subtitle);
  margin-bottom: var(--spacing-md);
}

/* 小屏幕适配 (600rpx 以下) */
@media screen and (max-width: 600rpx) {
  .standard-card {
    padding: 28rpx;
    margin-bottom: 24rpx;
  }
  
  .secondary-card {
    padding: 20rpx;
  }
  
  .btn-primary {
    height: 72rpx;
    font-size: 24rpx;
    padding: 0 24rpx;
  }
  
  .section-title {
    font-size: 24rpx;
    padding: 10rpx 16rpx;
  }
  
  /* 字体大小调整 */
  .font-title { font-size: 28rpx; }
  .font-subtitle { font-size: 24rpx; }
  .font-body { font-size: 22rpx; }
  .font-caption { font-size: 20rpx; }
  .font-small { font-size: 18rpx; }
  
  /* 间距调整 */
  .content {
    padding: 20rpx;
    padding-top: 12rpx;
  }
  
  /* 玩家卡片调整 */
  .player-item {
    padding: 16rpx 12rpx;
    min-height: 72rpx;
  }
  
  .player-avatar {
    width: 64rpx;
    height: 64rpx;
    font-size: 26rpx;
    margin-right: 12rpx;
  }
  
  /* 历史记录卡片调整 */
  .history-card {
    padding: 16rpx;
  }
  
  /* 弹窗调整 */
  .options-menu {
    width: 80%;
  }
  
  .round-detail-card {
    width: 92%;
    max-width: 500rpx;
  }
}

/* 中等屏幕适配 (600rpx - 1000rpx) */
@media screen and (min-width: 600rpx) and (max-width: 1000rpx) {
  .standard-card {
    padding: 32rpx;
    margin-bottom: 32rpx;
  }
  
  .btn-primary {
    height: 80rpx;
    font-size: 26rpx;
  }
}

/* 大屏幕优化 (1000rpx 以上) */
@media screen and (min-width: 1000rpx) {
  .content {
    max-width: 800rpx;
    margin: 0 auto;
    padding: 40rpx;
  }
  
  .standard-card {
    padding: 40rpx;
    margin-bottom: 40rpx;
  }
  
  .btn-primary {
    height: 88rpx;
    font-size: 28rpx;
  }
  
  .section-title {
    font-size: 30rpx;
    padding: 14rpx 24rpx;
  }
  
  /* 列表间距优化 */
  .player-list {
    gap: 20rpx;
  }
  
  .history-list {
    gap: 24rpx;
  }
  
  .feature-list {
    gap: 20rpx;
  }
}

/* 统一背景装饰元素 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05;
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

/* 统一页面根元素样式 */
.page-root {
  background-color: var(--background-beige) !important;
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

.page-container {
  min-height: 100vh;
  background-color: var(--background-beige);
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

/* 统一过渡动画 */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 统一点击效果 */
.clickable:active {
  transform: scale(0.98);
  background-color: rgba(248, 244, 233, 0.3);
}

.card-clickable:active {
  background-color: rgba(230, 196, 108, 0.25);
  border-color: rgba(212, 175, 55, 0.3);
}
