<!--score.wxml-->
<view class="container">
  <!-- 状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏 -->
  <view class="nav-bar" style="height: {{navBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <text class="back-icon">‹</text>
    </view>
    <view class="nav-center">
      <view class="nav-title">{{game ? game.name : '记分'}}</view>
      <view class="nav-subtitle" wx:if="{{gameMode === 'invite'}}">邀请模式</view>
    </view>
    <view class="nav-action" bindtap="showOptions">
      <text class="action-icon">⋮</text>
    </view>
  </view>
  
  <!-- 内容区域 - 添加scroll-view以支持滚动 -->
  <scroll-view class="score-scroll-view" scroll-y="true" style="height: {{contentHeight}}px;">
    <view class="content">
      <!-- 邀请模式提示 -->
      <view class="invite-mode-tip" wx:if="{{gameMode === 'invite'}}">
        <view class="tip-icon">📱</view>
        <view class="tip-content">
          <text class="tip-title">邀请模式</text>
          <text class="tip-desc">每位玩家都可以自行操作记分</text>
        </view>
      </view>
      
      <!-- 玩家分数卡片 -->
      <view class="player-section">
        <view class="section-title">玩家列表</view>
        <view class="score-cards" wx:if="{{game && game.players && game.players.length > 0}}">
          <view class="player-card {{item.selected ? 'active' : ''}}" 
                wx:for="{{game.players}}" 
                wx:key="id" 
                bindtap="selectPlayer" 
                data-index="{{index}}">
            <view class="player-info">
              <view class="player-avatar" style="background-color: {{item.color}}">
                {{item.name[0]}}
              </view>
              <view class="player-name">{{item.name}}</view>
            </view>
            <view class="player-score {{item.score > initialScore ? 'positive' : (item.score < initialScore ? 'negative' : '')}}">
              {{item.score}}
            </view>
          </view>
        </view>
        <view class="empty-state" wx:else>
          <text class="empty-text">无玩家数据</text>
          <button class="btn btn-primary" bindtap="reloadGame">重新加载</button>
        </view>
      </view>
      
      <!-- 快捷分数按钮 -->
      <view class="quick-score-section">
        <view class="quick-score-buttons">
          <view class="quick-score-row">
            <view class="quick-btn {{(currentMode === 'win' || (currentMode === 'custom' && customDirection === 'win')) ? 'active' : ''}}" bindtap="setScoreMode" data-mode="win">赢</view>
            <view class="quick-btn {{(currentMode === 'lose' || (currentMode === 'custom' && customDirection === 'lose')) ? 'active' : ''}}" bindtap="setScoreMode" data-mode="lose">输</view>
            <view class="quick-btn {{currentMode === 'custom' ? 'active' : ''}}" bindtap="setScoreMode" data-mode="custom">自定义</view>
          </view>
          
          <view class="quick-score-row" wx:if="{{currentMode !== 'custom'}}">
            <view class="quick-btn" 
                  wx:for="{{scoreChips}}" 
                  wx:key="*this"
                  bindtap="applyQuickScore" 
                  data-score="{{item}}">
              {{item}}
            </view>
          </view>
          
          <!-- 自定义分数输入 -->
          <view class="custom-score" wx:if="{{currentMode === 'custom'}}">
            <view class="custom-score-input">
              <view class="score-input-container">
                <view class="score-decrement" bindtap="decrementCustomScore">-</view>
                <input class="score-input" type="number" value="{{customScore}}" bindinput="onCustomScoreInput" />
                <view class="score-increment" bindtap="incrementCustomScore">+</view>
              </view>
              <view class="input-btn {{canApplyCustomScore ? 'active' : 'disabled'}}" bindtap="applyCustomScore">应用</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
