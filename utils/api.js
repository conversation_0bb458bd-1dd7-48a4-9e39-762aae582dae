// utils/api.js

// 引入路由工具
const router = require('./router');

// 获取全局APP实例
const app = getApp();

// API基础配置
const BASE_URL = app.globalData.apiBaseUrl;

/**
 * 发送API请求
 * @param {Object} options - 请求选项
 * @param {string} options.url - 请求路径 (不包含BASE_URL)
 * @param {string} options.method - 请求方法 (GET, POST, PUT, DELETE)
 * @param {Object} options.data - 请求数据
 * @param {boolean} options.requireAuth - 是否需要授权 (默认true)
 * @returns {Promise} 请求Promise
 */
const request = (options) => {
    const {
        url,
        method = 'GET',
        data = {},
        requireAuth = true
    } = options;

    // 构建完整URL
    const fullUrl = `${BASE_URL}${url}`;

    // 构建请求头
    const header = {
        'Content-Type': 'application/json'
    };

    // 如果需要授权，添加token
    if (requireAuth) {
        const session = wx.getStorageSync('session');
        if (session) {
            const sessionData = JSON.parse(session);
            if (sessionData && sessionData.token) {
                header.Authorization = `Bearer ${sessionData.token}`;
            }
        }
    }

    console.log(`发送${method}请求到: ${fullUrl}`, data);

    // 返回Promise
    return new Promise((resolve, reject) => {
        wx.request({
            url: fullUrl,
            method: method,
            data: data,
            header: header,
            success: (res) => {
                console.log(`请求成功，状态码: ${res.statusCode}`, res.data);

                // 处理成功响应
                if (res.statusCode === 200) {
                    if (res.data.code === 200) {
                        resolve(res.data);
                    } else {
                        // 业务错误
                        reject({
                            code: res.data.code,
                            message: res.data.message || '请求失败'
                        });
                    }
                } else if (res.statusCode === 401) {
                    // 未授权，清除本地session并提示重新登录
                    wx.removeStorageSync('session');
                    wx.showToast({
                        title: '登录已过期，请重新登录',
                        icon: 'none',
                        duration: 2000
                    });

                    // 跳转到登录页
                    setTimeout(() => {
                        router.reLaunch('gameList');
                    }, 1500);

                    reject({
                        code: 401,
                        message: '登录已过期，请重新登录'
                    });
                } else {
                    // 其他HTTP错误
                    reject({
                        code: res.statusCode,
                        message: `HTTP错误: ${res.statusCode}`
                    });
                }
            },
            fail: (err) => {
                console.error('请求失败:', err);
                // 请求失败（网络错误等）
                reject({
                    code: -1,
                    message: err.errMsg || '网络请求失败'
                });
            }
        });
    });
};

// 导出API方法
module.exports = {
    // 用户相关
    user: {
        login: (code, userInfo) => {
            return request({
                url: '/auth/login',
                method: 'POST',
                data: {
                    code,
                    userInfo
                },
                requireAuth: false
            });
        },
        refreshToken: (token) => {
            return request({
                url: '/auth/refresh',
                method: 'POST',
                data: {
                    token
                },
                requireAuth: false
            });
        },
        getUserInfo: () => {
            return request({
                url: '/user/info',
                method: 'GET'
            });
        }
    },

    // 游戏相关
    games: {
        // 创建游戏
        create: (gameData) => {
            return request({
                url: '/games',
                method: 'POST',
                data: gameData
            });
        },

        // 获取游戏列表
        list: (page = 1, size = 10) => {
            return request({
                url: `/games?page=${page}&size=${size}`,
                method: 'GET'
            });
        },

        // 获取游戏详情
        detail: (gameId) => {
            return request({
                url: `/games/${gameId}`,
                method: 'GET'
            });
        },

        // 删除游戏
        delete: (gameId) => {
            return request({
                url: `/games/${gameId}`,
                method: 'DELETE'
            });
        },

        // 提交得分
        submitScore: (gameId, scores) => {
            return request({
                url: `/games/${gameId}/scores`,
                method: 'POST',
                data: {
                    scores
                }
            });
        },

        // 删除轮次得分
        deleteRound: (gameId, roundId) => {
            return request({
                url: `/games/${gameId}/rounds/${roundId}`,
                method: 'DELETE'
            });
        },

        // 结算游戏
        settle: (gameId) => {
            return request({
                url: `/games/${gameId}/settle`,
                method: 'POST'
            });
        },

        // 获取游戏历史记录
        history: (params = {}) => {
            // 构建查询参数
            const queryParams = [];

            if (params.type) queryParams.push(`type=${params.type}`);
            if (params.start_date) queryParams.push(`start_date=${params.start_date}`);
            if (params.end_date) queryParams.push(`end_date=${params.end_date}`);
            if (typeof params.settled === 'boolean') queryParams.push(`settled=${params.settled}`);
            if (params.page) queryParams.push(`page=${params.page}`);
            if (params.size) queryParams.push(`size=${params.size}`);

            const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

            return request({
                url: `/games/history${queryString}`,
                method: 'GET'
            });
        }
    },

    // 邀请相关
    invites: {
        // 创建邀请
        create: (gameInfo) => {
            return request({
                url: '/invites',
                method: 'POST',
                data: {
                    gameInfo
                }
            });
        },

        // 获取邀请信息
        detail: (inviteId) => {
            return request({
                url: `/invites/${inviteId}`,
                method: 'GET'
            });
        },

        // 加入游戏邀请
        join: (inviteId, playerInfo) => {
            return request({
                url: `/invites/${inviteId}/join`,
                method: 'POST',
                data: playerInfo
            });
        },

        // 开始邀请游戏
        start: (inviteId) => {
            return request({
                url: `/invites/${inviteId}/start`,
                method: 'POST'
            });
        }
    },

    // 数据统计
    stats: {
        // 获取用户游戏统计
        user: () => {
            return request({
                url: '/stats/user',
                method: 'GET'
            });
        },

        // 获取游戏历史统计
        history: (type, timeRange = 'all') => {
            let url = '/stats/history';
            const params = [];

            if (type) params.push(`type=${type}`);
            if (timeRange) params.push(`timeRange=${timeRange}`);

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            return request({
                url: url,
                method: 'GET'
            });
        }
    },

    // 文件上传
    upload: {
        // 上传用户头像
        avatar: (filePath) => {
            return new Promise((resolve, reject) => {
                const session = wx.getStorageSync('session');
                let token = '';

                if (session) {
                    const sessionData = JSON.parse(session);
                    if (sessionData && sessionData.token) {
                        token = sessionData.token;
                    }
                }

                wx.uploadFile({
                    url: `${BASE_URL}/upload/avatar`,
                    filePath: filePath,
                    name: 'file',
                    header: {
                        'Authorization': token ? `Bearer ${token}` : ''
                    },
                    success: (res) => {
                        if (res.statusCode === 200) {
                            const data = JSON.parse(res.data);
                            if (data.code === 200) {
                                resolve(data);
                            } else {
                                reject({
                                    code: data.code,
                                    message: data.message || '上传失败'
                                });
                            }
                        } else {
                            reject({
                                code: res.statusCode,
                                message: `HTTP错误: ${res.statusCode}`
                            });
                        }
                    },
                    fail: (err) => {
                        console.error('上传失败:', err);
                        reject({
                            code: -1,
                            message: err.errMsg || '网络请求失败'
                        });
                    }
                });
            });
        }
    },

    // 系统配置
    config: {
        // 获取系统配置
        get: () => {
            return request({
                url: '/config',
                method: 'GET',
                requireAuth: false
            });
        }
    }
};