/**
 * 路由导航工具类
 * 统一管理小程序的页面跳转，解决幻觉bug问题
 */

// 页面路径配置
const routes = {
    // 主要Tab页面
    gameList: '/pages/gameList/index',
    history: '/pages/history/index',
    mine: '/pages/mine/index',

    // 非Tab页面
    score: '/pages/score/index',
    createGame: '/pages/createGame/index',
    gameHistory: '/pages/history/gameHistory',
};

/**
 * 普通页面跳转
 * @param {string} url 页面路径或路由名称
 * @param {Object} params 参数对象
 * @returns {Promise} 页面跳转的Promise
 */
function navigateTo(url, params = {}) {
    // 构建完整URL
    const fullUrl = buildUrl(url, params);
    console.log('navigateTo:', fullUrl);

    return new Promise((resolve, reject) => {
        wx.navigateTo({
            url: fullUrl,
            success: resolve,
            fail: (err) => {
                console.error('页面跳转失败:', err);
                reject(err);
            }
        });
    });
}

/**
 * Tab页面跳转
 * @param {string} url Tab页面路径或路由名称
 * @returns {Promise} 页面跳转的Promise
 */
function switchTab(url) {
    // 确保使用正确的路径
    const fullUrl = routes[url] || url;
    console.log('switchTab:', fullUrl);

    return new Promise((resolve, reject) => {
        wx.switchTab({
            url: fullUrl,
            success: resolve,
            fail: (err) => {
                console.error('Tab页面跳转失败:', err);
                reject(err);
            }
        });
    });
}

/**
 * 重定向到页面
 * @param {string} url 页面路径或路由名称
 * @param {Object} params 参数对象
 * @returns {Promise} 页面重定向的Promise
 */
function redirectTo(url, params = {}) {
    const fullUrl = buildUrl(url, params);
    console.log('redirectTo:', fullUrl);

    return new Promise((resolve, reject) => {
        wx.redirectTo({
            url: fullUrl,
            success: resolve,
            fail: reject
        });
    });
}

/**
 * 返回上一页
 * @param {number} delta 返回的页面数
 * @returns {void}
 */
function navigateBack(delta = 1) {
    wx.navigateBack({
        delta
    });
}

/**
 * 重启小程序
 * @param {string} url 重启后的页面路径或路由名称
 * @returns {void}
 */
function reLaunch(url = 'gameList') {
    // 确保使用正确的路径
    const fullUrl = routes[url] || url;
    console.log('reLaunch:', fullUrl);

    wx.reLaunch({
        url: fullUrl
    });
}

/**
 * 构建完整URL
 * @param {string} url 基础URL或路由名称
 * @param {Object} params 参数对象
 * @returns {string} 完整URL
 */
function buildUrl(url, params = {}) {
    // 如果url是routes中定义的key，则获取对应的路径
    if (routes[url]) {
        url = routes[url];
    }

    // 确保URL以/开头
    if (!url.startsWith('/')) {
        url = '/' + url;
    }

    // 添加参数
    if (params && Object.keys(params).length > 0) {
        const queryString = Object.keys(params)
            .filter(key => params[key] !== undefined && params[key] !== null)
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
            .join('&');

        if (queryString) {
            url += (url.includes('?') ? '&' : '?') + queryString;
        }
    }

    return url;
}

module.exports = {
    // 路由路径配置
    routes,

    // 导航方法
    navigateTo,
    switchTab,
    redirectTo,
    navigateBack,
    reLaunch,

    // 工具方法
    buildUrl
};